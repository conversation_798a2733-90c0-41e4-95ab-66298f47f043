body {
        font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif;
        margin: 2rem;
        background: var(--bg-secondary);
        color: var(--text-primary);
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background: var(--bg-primary);
        border-radius: 12px;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-primary);
    }
    .header { padding: 2rem; border-bottom: 1px solid var(--border-primary); }
    .breadcrumb-nav { display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1.5rem; font-size: 0.9rem; }
    .breadcrumb-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--accent-primary);
        text-decoration: none;
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s ease;
    }
    .breadcrumb-link:hover { background: var(--bg-accent); color: var(--accent-primary-hover); }
    .breadcrumb-icon { font-size: 1.1rem; }
    .breadcrumb-separator { color: var(--text-muted); margin: 0 0.25rem; }
    .breadcrumb-current { color: var(--text-muted); font-weight: 500; }
    .header-content { text-align: center; }
    .content { padding: 2rem; }
    .step { display: none; }
    .step.active { display: block; }
    .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
    .step-item {
        padding: 0.5rem 1rem;
        margin: 0 0.5rem;
        border-radius: 20px;
        background: var(--bg-tertiary);
        color: var(--text-inverse);
        border: 1px solid var(--border-primary);
    }
    .step-item.active { background: var(--accent-primary); color: var(--text-inverse); border-color: var(--text-inverse); }
    .step-item.completed { background: var(--accent-success); color: var(--text-inverse); border-color: var(--text-inverse); }
    .form-group { margin-bottom: 1.5rem; }
    .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary); }
    .form-group input, .form-group select, .form-group textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border-secondary);
      border-radius: 6px;
      font-size: 1rem;
      background: var(--bg-primary);
      color: var(--text-primary);
    }
    .form-group input:focus, .form-group select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }
    .form-group .help { font-size: 0.875rem; color: var(--text-muted); margin-top: 0.25rem; }
    .form-group .error { font-size: 0.875rem; color: var(--accent-danger); margin-top: 0.25rem; }
    .badge { display: inline-flex; align-items: center; gap: .3rem; padding: .3rem .55rem; border-radius: 999px; font-size: .72rem; font-weight: 600; text-transform: uppercase; letter-spacing: .05em; background: var(--bg-tertiary); color: var(--text-primary); }
    .badge-icon { font-size: .9rem; line-height: 1; }
    .badge-label { display: inline-block; }
    .badge-router { background: color-mix(in srgb, var(--accent-primary) 12%, var(--bg-accent)); color: var(--accent-primary); }
    .badge-switch { background: color-mix(in srgb, var(--accent-secondary) 12%, var(--bg-accent)); color: var(--accent-secondary); }
    .badge-ap { background: color-mix(in srgb, var(--accent-success) 12%, var(--bg-accent)); color: var(--accent-success); }
    .badge-firewall { background: color-mix(in srgb, var(--accent-danger) 12%, var(--bg-accent)); color: var(--accent-danger); }
    .badge-server { background: color-mix(in srgb, var(--accent-success) 10%, var(--bg-accent)); color: var(--accent-success); }
    .badge-gateway { background: color-mix(in srgb, var(--accent-warning) 12%, var(--bg-accent)); color: var(--accent-warning); }
    .badge-modem { background: color-mix(in srgb, var(--accent-primary) 10%, var(--bg-accent)); color: var(--accent-primary); }
    .badge-default { background: var(--bg-tertiary); color: var(--text-primary); }
    .template-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem; margin-top: 1.5rem; }
    .template-card {
        border: 2px solid var(--border-primary);
        border-radius: 12px;
        padding: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        transform: translateY(0);
        opacity: 1;
        background: var(--bg-primary);
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    .template-card:hover {
        border-color: var(--accent-primary);
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 123, 255, 0.15);
    }
    .template-card.selected {
        border-color: var(--accent-primary);
        background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg-accent) 100%);
        transform: scale(1.02);
        box-shadow: 0 8px 24px rgba(0, 123, 255, 0.2);
    }
    .template-card.keyboard-selected {
        border-color: var(--accent-success);
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
    }
    .template-card.fade-out {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
        pointer-events: none;
    }
    .template-card.fade-out-others {
        opacity: 0.3;
        transform: translateY(10px) scale(0.98);
        pointer-events: none;
    }

    .template-card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1.25rem 1.25rem 0.75rem;
        gap: 1rem;
    }
    .template-title-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    .template-name {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1.3;
    }
    .template-icon {
        font-size: 2rem;
        opacity: 0.8;
        flex-shrink: 0;
    }

    .template-description {
        padding: 0 1.25rem;
        margin-bottom: 1rem;
    }
    .template-description p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .template-meta {
        display: flex;
        gap: 1rem;
        padding: 0 1.25rem;
        margin-bottom: 1rem;
    }
    .meta-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    .meta-label {
        font-size: 0.75rem;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }
    .meta-value {
        font-size: 0.85rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .template-card-footer {
        padding: 0.75rem 1.25rem;
        background: var(--bg-secondary);
        border-top: 1px solid var(--bg-tertiary);
        text-align: center;
    }
    .select-hint {
        font-size: 0.8rem;
        color: var(--accent-secondary);
        font-style: italic;
    }
    .template-card:hover .select-hint {
        color: var(--accent-primary);
    }
    .mode-toggle { display: flex; gap: 0.5rem; margin: 1.5rem 0 1rem; }
    .mode-toggle .mode-btn { flex: 1; }
    .discovery-section { background: var(--bg-secondary); border-radius: 8px; padding: 1.5rem; margin: 1rem 0; }
    .device-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1rem; margin-top: 1rem; }
    .device-card { border: 1px solid var(--border-secondary); border-radius: 8px; padding: 1rem; background: white; cursor: pointer; transition: all 0.2s; }
    .device-card:hover { border-color: var(--accent-primary); }
    .device-card.selected { border-color: var(--accent-primary); background: var(--bg-accent); }
    .device-card.keyboard-selected { border-color: var(--accent-success); box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25); }
    .device-info { display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem; }
    .device-ip { font-weight: 500; }
    .device-ping { color: var(--accent-success); font-size: 0.875rem; }
    .device-services { display: flex; flex-wrap: wrap; gap: 0.25rem; }
    .service-tag { background: var(--bg-tertiary); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; }
    .suggestions { margin-top: 0.5rem; }
    .suggestion-tag { background: var(--accent-primary); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; margin-right: 0.25rem; }
    .buttons { display: flex; justify-content: space-between; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--border-primary); }
    .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 6px; font-size: 1rem; cursor: pointer; transition: all 0.2s; }
    .btn-primary { background: var(--accent-primary); color: white; }
    .btn-primary:hover { background: var(--accent-primary-hover); }
    .btn-secondary { background: var(--accent-secondary); color: white; }
    .btn-secondary:hover { background: var(--accent-secondary); }
    .btn-outline { background: transparent; color: var(--accent-primary); border: 1px solid var(--accent-primary); }
    .btn-outline:hover { background: var(--accent-primary); color: white; }

    /* Wizard step transitions */
    .step { opacity: 1; transform: translateX(0); transition: all 0.4s ease; }
    .step.slide-out-left { opacity: 0; transform: translateX(-50px); }
    .step.slide-in-right { opacity: 0; transform: translateX(50px); }
    .step.slide-in-right.active { opacity: 1; transform: translateX(0); }

    /* Template selection animations */
    .template-selection-complete .template-grid {
        animation: fadeOutAndSlide 0.5s ease forwards;
    }

    @keyframes fadeOutAndSlide {
        0% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-30px); }
    }

    /* Step indicator animations */
    .step-item { transition: all 0.3s ease; }
    .step-item.active { transform: scale(1.05); }

    /* Discovery section entrance animation */
    .discovery-section.animate-in {
        animation: slideInFromBottom 0.6s ease 0.5s both;
    }

    @keyframes slideInFromBottom {
        0% { opacity: 0; transform: translateY(30px); }
        100% { opacity: 1; transform: translateY(0); }
    }
    .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    .loading { text-align: center; padding: 2rem; }
    .spinner { border: 3px solid var(--bg-tertiary); border-top: 3px solid var(--accent-primary); border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 1rem; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    .validation-results { margin-top: 1rem; }
    .validation-item { padding: 0.5rem; margin: 0.25rem 0; border-radius: 4px; }
    .validation-success { background: color-mix(in srgb, var(--accent-success) 12%, transparent); color: var(--accent-success); border: 1px solid color-mix(in srgb, var(--accent-success) 50%, transparent); }
    .validation-warning { background: color-mix(in srgb, var(--accent-warning) 15%, transparent); color: var(--accent-warning); border: 1px solid color-mix(in srgb, var(--accent-warning) 55%, transparent); }
    .validation-error { background: color-mix(in srgb, var(--accent-danger) 12%, transparent); color: var(--accent-danger); border: 1px solid color-mix(in srgb, var(--accent-danger) 50%, transparent); }
    .hidden { display: none; }
    .ssh-key-field-controls { display: flex; gap: 0.5rem; align-items: center; margin-bottom: 0.75rem; }
    .ssh-key-field-controls select { flex: 1; }
    #ssh-key-path-input { margin-top: 0.75rem; }
    .modal-overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.55); display: none; align-items: center; justify-content: center; padding: 1.5rem; z-index: 1000; }
    .modal-overlay.active { display: flex; }
    .modal { background: white; border-radius: 10px; width: min(720px, 95%); max-height: 92vh; display: flex; flex-direction: column; box-shadow: 0 20px 45px rgba(15,23,42,0.25); }
    .modal-header { display: flex; justify-content: space-between; align-items: center; padding: 1rem 1.5rem; border-bottom: 1px solid var(--border-primary); }
    .modal-body { padding: 1.5rem; overflow-y: auto; }
    .key-list { display: flex; flex-direction: column; gap: 0.75rem; margin-bottom: 1.5rem; }
    .key-item { border: 1px solid var(--border-secondary); border-radius: 8px; padding: 0.75rem 1rem; display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
    .key-meta { display: flex; flex-direction: column; gap: 0.15rem; }
    .key-meta span { font-size: 0.9rem; color: var(--text-secondary); }
    .key-actions { display: flex; gap: 0.5rem; }
    .key-actions .btn { padding: 0.5rem 0.75rem; font-size: 0.9rem; }
    .key-add { border-top: 1px solid var(--border-primary); padding-top: 1.5rem; display: flex; flex-direction: column; gap: 0.75rem; }
    .key-add textarea { min-height: 140px; font-family: ui-monospace, SFMono-Regular, SFMono, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    .key-viewer { background: var(--bg-secondary); border-radius: 6px; padding: 1rem; font-family: ui-monospace, monospace; font-size: 0.85rem; white-space: pre-wrap; word-break: break-all; }
    .key-empty { color: var(--text-secondary); font-size: 0.9rem; }


/* ==========================
   THEME OVERRIDES FOR WIZARD
   Use variables from themes.css instead of hardcoded colours
   ========================== */

/* Badges */
.badge {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}
.badge-router {
  background: color-mix(in srgb, var(--accent-primary) 12%, var(--bg-accent));
  color: var(--accent-primary);
}
.badge-switch {
  background: color-mix(in srgb, var(--accent-secondary) 12%, var(--bg-accent));
  color: var(--accent-secondary);
}
.badge-ap {
  background: color-mix(in srgb, var(--accent-success) 12%, var(--bg-accent));
  color: var(--accent-success);
}
.badge-firewall {
  background: color-mix(in srgb, var(--accent-danger) 12%, var(--bg-accent));
  color: var(--accent-danger);
}
.badge-server {
  background: color-mix(in srgb, var(--accent-success) 10%, var(--bg-accent));
  color: var(--accent-success);
}
.badge-gateway {
  background: color-mix(in srgb, var(--accent-warning) 12%, var(--bg-accent));
  color: var(--accent-warning);
}
.badge-modem {
  background: color-mix(in srgb, var(--accent-primary) 10%, var(--bg-accent));
  color: var(--accent-primary);
}
.badge-default {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Template cards */
.template-card {
  border: 2px solid var(--border-primary);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
}
.template-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-md);
}
.template-card.selected {
  border-color: var(--accent-primary);
  background: var(--gradient-primary);
  box-shadow: var(--shadow-lg);
}
.template-card.keyboard-selected {
  border-color: var(--accent-success);
  /* ring using the success colour */
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-success) 25%, transparent);
}
.template-title-section .template-name,
.template-name { color: var(--text-primary); }
.template-description p { color: var(--text-secondary); }
.meta-label { color: var(--text-muted); }
.meta-value { color: var(--text-primary); }
.template-card-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
}
.select-hint { color: var(--text-muted); }
.template-card:hover .select-hint { color: var(--accent-primary); }

/* Discovery section */
.discovery-section { background: var(--bg-secondary); }

/* Discovered device cards */
.device-card {
  border: 1px solid var(--border-secondary);
  background: var(--bg-primary);
}
.device-card:hover { border-color: var(--accent-primary); }
.device-card.selected {
  border-color: var(--accent-primary);
  background: var(--bg-accent);
}
.device-card.keyboard-selected {
  border-color: var(--accent-success);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--accent-success) 25%, transparent);
}
.device-ping { color: var(--accent-success); }
.service-tag { background: var(--bg-tertiary); }
.suggestion-tag {
  background: var(--accent-primary);
  color: var(--text-inverse);
}

/* Footer buttons row */
.buttons { border-top: 1px solid var(--border-primary); }

/* Buttons */
.btn { transition: background .2s, color .2s, border-color .2s; }
.btn-primary {
  background: var(--accent-primary);
  color: var(--text-inverse);
  border: 1px solid var(--accent-primary);
}
.btn-primary:hover {
  background: var(--accent-primary-hover);
  border-color: var(--accent-primary-hover);
}
.btn-secondary {
  background: var(--accent-secondary);
  border: 1px solid var(--accent-secondary);
  color: var(--text-inverse);
}
.btn-secondary:hover { filter: brightness(0.92); }
.btn-outline {
  background: transparent;
  color: var(--accent-primary);
  border: 1px solid var(--accent-primary);
}
.btn-outline:hover {
  background: var(--accent-primary);
  color: var(--text-inverse);
}

/* Spinner */
.spinner {
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--accent-primary);
}

/* Validation states */
.validation-success {
  background: color-mix(in srgb, var(--accent-success) 12%, transparent);
  color: var(--accent-success);
  border: 1px solid color-mix(in srgb, var(--accent-success) 50%, transparent);
}
.validation-warning {
  background: color-mix(in srgb, var(--accent-warning) 15%, transparent);
  color: var(--accent-warning);
  border: 1px solid color-mix(in srgb, var(--accent-warning) 55%, transparent);
}
.validation-error {
  background: color-mix(in srgb, var(--accent-danger) 12%, transparent);
  color: var(--accent-danger);
  border: 1px solid color-mix(in srgb, var(--accent-danger) 50%, transparent);
}

/* Modal and key manager */
.modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
}
.modal-header { border-bottom: 1px solid var(--border-primary); }
.modal-body { color: var(--text-primary); }
.key-item { border: 1px solid var(--border-secondary); }
.key-meta span { color: var(--text-secondary); }
.key-add { border-top: 1px solid var(--border-primary); }
.key-viewer { background: var(--bg-secondary); }
.key-empty { color: var(--text-secondary); }

/* END THEME OVERRIDES */
