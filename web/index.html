<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PulseOps</title>
  <link rel="stylesheet" href="/css/themes.css" />
  <link rel="stylesheet" href="/css/style.css" />
  <script src="/js/theme-manager.js"></script>
</head>
<body>
  <div class="app-shell">
    <header class="app-header">
      <div class="app-header-top">
        <div>
          <h1>PulseOps</h1>
          <p class="muted">Monitoring &amp; orchestration for your network estate.</p>
        </div>
        <div class="header-actions">
          <a href="/wizard.html" style="text-decoration: none;">
            <button class="btn btn-primary" type="button">➕ Add Device</button>
          </a>
          <div class="menu-wrapper">
            <button type="button" class="menu-trigger" id="user-menu-trigger" aria-label="User menu">
              👤 <span id="username-display">Admin</span>
            </button>
            <div class="menu-list hidden" id="user-menu">
              <button type="button" class="menu-item" id="logout-btn">Sign Out</button>
            </div>
          </div>
        </div>
      </div>
      <nav class="nav-tabs" role="tablist">
        <button type="button" class="nav-tab active" data-view="overview">Overview</button>
        <button type="button" class="nav-tab" data-view="logs">Activity Logs</button>
        <button type="button" class="nav-tab" data-view="devices">Devices</button>
        <button type="button" class="nav-tab" data-view="keys">SSH Keys</button>
        <button type="button" class="nav-tab" data-view="settings">Settings</button>
        <button type="button" class="nav-tab" data-view="insights">Device Insights</button>
      </nav>
    </header>

    <main class="app-main">
      <section id="view-overview" class="view-section">
        <div id="overview-empty" class="empty-state hidden">No devices yet. Add one to start monitoring.</div>
        <div id="devices" class="grid"></div>
      </section>

      <section id="view-logs" class="view-section hidden">
        <div class="panel">
          <form id="logs-filter-form" class="logs-filters" autocomplete="off">
            <div class="settings-field">
              <label for="logs-source">Source</label>
              <select id="logs-source">
                <option value="all">All sources</option>
                <option value="device">Devices</option>
                <option value="system">PulseOps</option>
              </select>
            </div>
            <div class="settings-field">
              <label for="logs-device-kind">Device type</label>
              <select id="logs-device-kind">
                <option value="">All types</option>
              </select>
            </div>
            <div class="settings-field">
              <label for="logs-device-id">Device</label>
              <select id="logs-device-id">
                <option value="">All devices</option>
              </select>
            </div>
            <div class="settings-field">
              <label for="logs-level">Log level</label>
              <select id="logs-level">
                <option value="">All levels</option>
                <option value="info">Info</option>
                <option value="warn">Warn</option>
                <option value="error">Error</option>
              </select>
            </div>
            <div class="settings-field">
              <label for="logs-ip-range">Device IP range</label>
              <input type="text" id="logs-ip-range" placeholder="***********/24 or 10.0.0.*" />
            </div>
            <div class="settings-field">
              <label for="logs-search">Search</label>
              <input type="text" id="logs-search" placeholder="Message, device or level" />
            </div>
            <div class="settings-field">
              <label>&nbsp;</label>
              <div class="form-inline">
                <button type="submit" class="btn btn-primary">Apply filters</button>
                <button type="button" class="btn btn-secondary" id="logs-reset">Reset</button>
              </div>
            </div>
          </form>
        </div>
        <div id="logs-status" class="muted"></div>
        <div id="logs-results" class="logs-list"></div>
      </section>

      <section id="view-devices" class="view-section hidden">
        <div class="device-toolbar">
          <p class="muted">Export device definitions as JSON snapshots for backup or restore.</p>
          <div class="device-toolbar-buttons">
            <button type="button" class="btn btn-outline" id="device-select-all">Select all</button>
            <button type="button" class="btn btn-secondary" id="device-export-selected" disabled>Export selected</button>
            <button type="button" class="btn btn-primary" id="device-export-all">Export all</button>
          </div>
        </div>
        <div class="panel">
          <table class="device-table" aria-describedby="device-table-caption">
            <caption id="device-table-caption" class="sr-only">List of devices</caption>
            <thead>
              <tr>
                <th style="width:42px;"><input type="checkbox" id="device-table-master" aria-label="Select all devices" /></th>
                <th>Name</th>
                <th>Host</th>
                <th>Kind</th>
                <th>Platform</th>
                <th>User</th>
              </tr>
            </thead>
            <tbody id="device-table-body">
              <tr><td colspan="6" class="muted">Loading devices...</td></tr>
            </tbody>
          </table>
        </div>
      </section>

      <section id="view-keys" class="view-section hidden">
        <div class="keys-toolbar">
          <div>
            <h2 style="margin: 0 0 0.5rem 0;">SSH Key Manager</h2>
            <p class="muted">Manage SSH keys used for device authentication. Keys are stored encrypted on disk.</p>
          </div>
          <div class="keys-toolbar-buttons">
            <button type="button" class="btn btn-primary" id="keys-add-btn">➕ Add New Key</button>
            <button type="button" class="btn btn-secondary" id="keys-refresh-btn">🔄 Refresh</button>
          </div>
        </div>
        <div id="keys-content">
          <div id="keys-loading" class="empty-state">Loading SSH keys...</div>
          <div id="keys-empty" class="empty-state hidden">No SSH keys configured yet. Add one to get started.</div>
          <div id="keys-list" class="keys-grid hidden"></div>
        </div>
      </section>

      <section id="view-settings" class="view-section hidden">
        <form id="settings-form" class="settings-form" autocomplete="off">
          <div class="settings-group">
            <h3>Appearance &amp; account</h3>
            <div class="settings-row">
              <div class="settings-field">
                <label for="settings-theme">Theme</label>
                <select id="settings-theme">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="retro">Retro Terminal</option>
                  <option value="sophisticated">Sophisticated Blue</option>
                  <option value="system">Match system</option>
                </select>
                <div class="settings-note">Choose your preferred visual theme. System will follow your browser's preference.</div>
              </div>
              <div class="settings-field">
                <label for="settings-account-name">Account name</label>
                <input type="text" id="settings-account-name" placeholder="Network operations" />
              </div>
              <div class="settings-field">
                <label for="settings-account-email">Account email</label>
                <input type="email" id="settings-account-email" placeholder="<EMAIL>" />
              </div>
            </div>
          </div>

          <div class="settings-group">
            <h3>Notifications</h3>
            <div class="settings-field">
              <label><input type="checkbox" id="settings-email-enabled" /> Enable email notifications</label>
              <div class="settings-note">PulseOps will use the SMTP details below for alerts.</div>
            </div>
            <div class="settings-row">
              <div class="settings-field">
                <label for="settings-email-host">SMTP host</label>
                <input type="text" id="settings-email-host" placeholder="smtp.example.com" />
              </div>
              <div class="settings-field">
                <label for="settings-email-port">SMTP port</label>
                <input type="number" id="settings-email-port" min="1" max="65535" placeholder="587" />
              </div>
              <div class="settings-field">
                <label for="settings-email-username">SMTP username</label>
                <input type="text" id="settings-email-username" placeholder="<EMAIL>" />
              </div>
              <div class="settings-field">
                <label for="settings-email-password">SMTP password</label>
                <input type="password" id="settings-email-password" placeholder="Leave blank to keep current" />
                <label class="settings-note"><input type="checkbox" id="settings-email-clear" /> Clear saved password</label>
                <div class="settings-note" id="settings-email-password-note"></div>
              </div>
            </div>
            <div class="settings-field">
              <label><input type="checkbox" id="settings-web-enabled" /> Enable browser notifications</label>
              <div class="settings-note">Requires acceptance in the browser when prompted.</div>
            </div>
          </div>

          <div class="settings-actions">
            <button type="submit" class="btn btn-primary" id="settings-save">Save settings</button>
            <span id="settings-status" class="muted"></span>
          </div>
        </form>
      </section>

      <section id="view-insights" class="view-section hidden">
        <div class="panel">
          <div class="insights-header">
            <select id="insights-device-select">
              <option value="">Select a device…</option>
            </select>
            <button type="button" class="btn btn-secondary" id="insights-refresh" disabled>Refresh</button>
          </div>
          <div id="insights-empty" class="empty-state">Pick a device to inspect historical metrics and activity.</div>
          <div id="insights-content" class="hidden">
            <div class="insights-summary">
              <div class="insights-meta" id="insights-meta"></div>
            </div>
            <div class="insights-chart">
              <canvas id="insights-chart-canvas" width="720" height="260"></canvas>
            </div>
            <div>
              <h3 style="margin:1rem 0 .5rem;">Recent activity</h3>
              <div id="insights-logs" class="logs-list"></div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <div id="confirm-modal" class="modal-backdrop hidden" role="dialog" aria-modal="true" aria-labelledby="confirm-modal-title">
    <div class="modal">
      <h2 id="confirm-modal-title">Confirm action</h2>
      <p id="confirm-modal-message">Are you sure?</p>
      <div class="modal-actions" id="confirm-modal-actions">
        <div class="modal-actions-extra" id="confirm-modal-extra"></div>
        <div class="modal-actions-main">
          <button type="button" id="confirm-modal-cancel">Cancel</button>
          <button type="button" class="btn btn-primary" id="confirm-modal-confirm">Confirm</button>
        </div>
      </div>
    </div>
  </div>

  <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>

  <div id="edit-device-overlay" class="drawer-backdrop" role="dialog" aria-modal="true" aria-labelledby="edit-device-title">
    <div class="drawer-panel">
      <div class="drawer-header">
        <div>
          <h2 class="drawer-title" id="edit-device-title">Edit Device</h2>
          <p class="muted" id="edit-device-subtitle">Adjust configuration and validate before saving.</p>
        </div>
        <button type="button" class="drawer-close" id="edit-close-btn" aria-label="Close editor">×</button>
      </div>
      <div class="drawer-body">
        <div id="edit-loading-state" class="edit-loading hidden">
          <div class="spinner"></div>
          <p>Loading device...</p>
        </div>
        <div id="edit-content" class="hidden">
          <div class="stepper">
            <div class="stepper-item" data-edit-step="1">Configuration</div>
            <div class="stepper-item" data-edit-step="2">Validation</div>
          </div>
          <div class="edit-step" id="edit-step-1">
            <form id="edit-device-form"></form>
          </div>
          <div class="edit-step" id="edit-step-2">
            <div id="edit-validation-loading" class="edit-loading hidden">
              <div class="spinner"></div>
              <p>Validating device...</p>
            </div>
            <div id="edit-validation-results" class="validation-results hidden"></div>
            <div id="edit-summary" class="edit-summary hidden"></div>
          </div>
        </div>
      </div>
      <div class="drawer-footer">
        <button type="button" class="btn btn-secondary" id="edit-back-btn">Back</button>
        <div style="display:flex; gap:.5rem;">
          <button type="button" class="btn btn-outline" id="edit-validate-btn">Validate</button>
          <button type="button" class="btn btn-primary" id="edit-save-btn">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

  <div id="edit-ssh-key-modal" class="key-modal-backdrop" role="dialog" aria-modal="true" aria-labelledby="edit-ssh-key-title">
    <div class="key-modal">
      <header>
        <h3 id="edit-ssh-key-title" style="margin:0; font-size:1.2rem;">SSH Key Manager</h3>
        <button type="button" class="drawer-close" id="edit-ssh-key-close" aria-label="Close SSH key manager">×</button>
      </header>
      <div class="modal-body">
        <div class="key-list" id="edit-ssh-key-list"></div>
        <div style="border-top:1px solid #eee; padding-top:1rem; display:flex; flex-direction:column; gap:1rem;">
          <h4 style="margin:0;">Add New Key</h4>
          <div class="form-group" style="margin-bottom:0;">
            <label for="edit-new-ssh-key-name">Key Name</label>
            <input type="text" id="edit-new-ssh-key-name" placeholder="e.g., datacenter-router-key">
          </div>
          <div class="form-group" style="margin-bottom:0;">
            <label for="edit-new-ssh-key-content">Private Key (PEM)</label>
            <textarea id="edit-new-ssh-key-content" placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"></textarea>
          </div>
          <div style="display:flex; gap:.75rem; align-items:center;">
            <button type="button" class="btn btn-primary" id="edit-save-ssh-key-btn">Save Key</button>
            <span class="muted" style="font-size:.85rem;">Keys are stored encrypted on disk.</span>
          </div>
        </div>
        <div id="edit-ssh-key-viewer" class="key-viewer hidden"></div>
      </div>
    </div>
  </div>

  <div id="add-key-modal" class="modal-backdrop hidden" role="dialog" aria-modal="true" aria-labelledby="add-key-title">
    <div class="modal" style="max-width: 520px;">
      <h2 id="add-key-title">Add SSH Key</h2>
      <form id="add-key-form">
        <div class="settings-field">
          <label for="add-key-name">Key Name</label>
          <input type="text" id="add-key-name" placeholder="e.g., datacenter-router-key" required>
          <div class="settings-note">A descriptive name to identify this key</div>
        </div>
        <div class="settings-field">
          <label for="add-key-content">Private Key (PEM format)</label>
          <textarea id="add-key-content" rows="8" placeholder="-----BEGIN OPENSSH PRIVATE KEY-----" required style="font-family: monospace; font-size: 0.9rem;"></textarea>
          <div class="settings-note">Paste your private key content here. Keys are stored encrypted on disk.</div>
        </div>
      </form>
      <div class="modal-actions">
        <button type="button" id="add-key-cancel">Cancel</button>
        <button type="button" class="btn btn-primary" id="add-key-save">Save Key</button>
      </div>
    </div>
  </div>

  <script>
    async function json(url, opts={}) { const r=await fetch(url, opts); if(!r.ok){throw new Error(await r.text())}; return r.json(); }
    function escapeHTML(value){
      return String(value ?? '').replace(/[&<>"']/g, (match) => {
        switch(match){
          case '&': return '&amp;';
          case '<': return '&lt;';
          case '>': return '&gt;';
          case '"': return '&quot;';
          case "'": return '&#39;';
          default: return match;
        }
      });
    }

    // Authentication state
    let authState = { setup_completed: false, authenticated: false, user: null };
    const userMenuTrigger = document.getElementById('user-menu-trigger');
    const userMenu = document.getElementById('user-menu');
    const usernameDisplay = document.getElementById('username-display');
    const logoutBtn = document.getElementById('logout-btn');

    // Check authentication status
    async function checkAuthStatus() {
      try {
        const response = await fetch('/api/auth/status');
        authState = await response.json();

        if (!authState.setup_completed) {
          window.location.href = '/setup.html';
          return;
        }

        if (!authState.authenticated) {
          window.location.href = '/login.html';
          return;
        }

        // Update UI with user info
        if (authState.user) {
          usernameDisplay.textContent = authState.user.username;
        }
      } catch (error) {
        console.error('Failed to check auth status:', error);
        window.location.href = '/login.html';
      }
    }

    // Handle logout
    async function handleLogout() {
      try {
        await fetch('/api/auth/logout', { method: 'POST' });
        window.location.href = '/login.html';
      } catch (error) {
        console.error('Logout failed:', error);
        window.location.href = '/login.html';
      }
    }

    // User menu toggle
    function toggleUserMenu() {
      userMenu.classList.toggle('hidden');
    }

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!userMenuTrigger.contains(e.target) && !userMenu.contains(e.target)) {
        userMenu.classList.add('hidden');
      }
    });

    // Event listeners
    if (userMenuTrigger) userMenuTrigger.addEventListener('click', toggleUserMenu);
    if (logoutBtn) logoutBtn.addEventListener('click', handleLogout);
    function formatTimestamp(value){
      if(!value){ return '—'; }
      let date = new Date(value);
      if(Number.isNaN(date.getTime())){
        const isoCandidate = String(value).replace(' ', 'T') + 'Z';
        date = new Date(isoCandidate);
      }
      if(Number.isNaN(date.getTime())){ return String(value); }
      return date.toLocaleString();
    }
    function truncateText(value, max){
      const str = String(value ?? '');
      if(str.length <= max){ return str; }
      return str.slice(0, Math.max(0, max - 1)) + '…';
    }
    function formatLogTime(value){
      if(!value){ return '--'; }
      const date = new Date(value);
      if(Number.isNaN(date.getTime())){ return '--'; }
      const now = new Date();
      const diff = now - date;
      const opts = { hour: '2-digit', minute: '2-digit' };
      if(diff < 3600_000){
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
      }
      if(diff < 24 * 3600_000){
        return date.toLocaleTimeString([], opts);
      }
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' + date.toLocaleTimeString([], opts);
    }
    function el(tag, attrs={}, ...kids){
      const e=document.createElement(tag);
      for(const [k,v] of Object.entries(attrs)){
        if(k==='class'){
          e.className=v;
        } else if(k.startsWith('on') && typeof v === 'function'){
          e.addEventListener(k.slice(2).toLowerCase(), v);
        } else {
          e.setAttribute(k,v);
        }
      }
      kids.flat().forEach(k=>{
        if(typeof k==='string') e.appendChild(document.createTextNode(k));
        else if(k) e.appendChild(k);
      });
      return e;
    }

    const countdownIntervals = new Map();
    let devices = [];
    let latestInterval;
    let openMenuState = null;
    const confirmOverlay = document.getElementById('confirm-modal');
    const confirmTitleEl = document.getElementById('confirm-modal-title');
    const confirmMessageEl = document.getElementById('confirm-modal-message');
    const confirmCancelBtn = document.getElementById('confirm-modal-cancel');
    const confirmConfirmBtn = document.getElementById('confirm-modal-confirm');
    const confirmExtraContainer = document.getElementById('confirm-modal-extra');
    const confirmState = { onConfirm: null, extraButtons: [] };
    const toastContainer = document.getElementById('toast-container');
    const EDIT_STEPS = { CONFIG: 1, VALIDATION: 2 };
    const SSH_KEY_REFERENCE_PREFIX = 'sshkey:';
    const SSH_KEY_PATH_OPTION = '__path__';
    let templatesCache = null;
    let sshKeysCache = [];
    let sshKeysLoadErrorNotified = false;
    const editState = {
      open: false,
      step: EDIT_STEPS.CONFIG,
      deviceId: null,
      template: null,
      deviceConfig: { meta: {} },
      validation: null,
      isValidating: false,
      isSaving: false
    };
    const editOverlay = document.getElementById('edit-device-overlay');
    const editForm = document.getElementById('edit-device-form');
    const editLoadingState = document.getElementById('edit-loading-state');
    const editContent = document.getElementById('edit-content');
    const editValidationLoading = document.getElementById('edit-validation-loading');
    const editValidationResults = document.getElementById('edit-validation-results');
    const editSummary = document.getElementById('edit-summary');
    const editBackBtn = document.getElementById('edit-back-btn');
    const editValidateBtn = document.getElementById('edit-validate-btn');
    const editSaveBtn = document.getElementById('edit-save-btn');
    const editCloseBtn = document.getElementById('edit-close-btn');
    const editTitle = document.getElementById('edit-device-title');
    const editSubtitle = document.getElementById('edit-device-subtitle');
    const editSSHModal = document.getElementById('edit-ssh-key-modal');
    const editSSHList = document.getElementById('edit-ssh-key-list');
    const editSSHViewer = document.getElementById('edit-ssh-key-viewer');
    const editSSHCloseBtn = document.getElementById('edit-ssh-key-close');
    const editSaveSSHKeyBtn = document.getElementById('edit-save-ssh-key-btn');
    const editNewSSHKeyName = document.getElementById('edit-new-ssh-key-name');
    const editNewSSHKeyContent = document.getElementById('edit-new-ssh-key-content');
    const deviceTasks = new Map();
    const taskRefreshTimers = new Map();
    const expandedTaskPanels = new Set();
    const logExpansionState = new Map();
    const TASK_PREVIEW_LIMIT = 1;
    const TASK_EXPANDED_LIMIT = 15;
    const TASK_LABELS = {
      reboot: 'Reboot',
      refresh_firewall: 'Refresh firewall',
      refresh_wireless: 'Refresh wireless'
    };
    const METRIC_SERIES = [
      { key: 'ping_ms', label: 'Ping', unit: 'ms', color: '#2563eb' },
      { key: 'iperf_mbps', label: 'Bandwidth', unit: 'Mbps', color: '#1d7a46' }
    ];
    const DEVICE_KIND_ALIASES = {
      ap: 'access_point',
      accesspoint: 'access_point',
      wifi: 'access_point',
      wireless: 'access_point',
      wap: 'access_point',
      routerboard: 'router',
      firewall_appliance: 'firewall',
      utm: 'firewall',
      switchgear: 'switch',
      edge_switch: 'switch'
    };
    const DEVICE_KIND_META = {
      router: { icon: '🛣️', className: 'badge-router', label: 'Router' },
      switch: { icon: '🔀', className: 'badge-switch', label: 'Switch' },
      access_point: { icon: '📡', className: 'badge-ap', label: 'Access Point' },
      firewall: { icon: '🛡️', className: 'badge-firewall', label: 'Firewall' },
      server: { icon: '🖥️', className: 'badge-server', label: 'Server' },
      gateway: { icon: '🚪', className: 'badge-gateway', label: 'Gateway' },
      modem: { icon: '📶', className: 'badge-modem', label: 'Modem' },
      default: { icon: '⚙️', className: 'badge-default', label: 'Device' }
    };
    const DEVICE_TASKS = {
      router: ['reboot', 'refresh_firewall'],
      firewall: ['reboot', 'refresh_firewall'],
      gateway: ['reboot', 'refresh_firewall'],
      access_point: ['reboot', 'refresh_wireless'],
      switch: ['reboot'],
      server: ['reboot'],
      modem: ['reboot'],
      openwrt: ['reboot', 'refresh_firewall', 'refresh_wireless'],
      edgeos: ['reboot', 'refresh_firewall'],
      huawei: ['reboot'],
      default: ['reboot']
    };
    const overviewEmptyState = document.getElementById('overview-empty');
    const viewSections = {
      overview: document.getElementById('view-overview'),
      logs: document.getElementById('view-logs'),
      devices: document.getElementById('view-devices'),
      keys: document.getElementById('view-keys'),
      settings: document.getElementById('view-settings'),
      insights: document.getElementById('view-insights')
    };
    const navTabs = Array.from(document.querySelectorAll('.nav-tab'));
    const viewState = { current: 'overview' };

    const logsForm = document.getElementById('logs-filter-form');
    const logsResults = document.getElementById('logs-results');
    const logsStatus = document.getElementById('logs-status');
    const logsSourceSelect = document.getElementById('logs-source');
    const logsDeviceKindSelect = document.getElementById('logs-device-kind');
    const logsDeviceSelect = document.getElementById('logs-device-id');
    const logsLevelSelect = document.getElementById('logs-level');
    const logsIPInput = document.getElementById('logs-ip-range');
    const logsSearchInput = document.getElementById('logs-search');
    const logsResetBtn = document.getElementById('logs-reset');

    const deviceTableBody = document.getElementById('device-table-body');
    const deviceTableMaster = document.getElementById('device-table-master');
    const deviceSelectAllBtn = document.getElementById('device-select-all');
    const deviceExportSelectedBtn = document.getElementById('device-export-selected');
    const deviceExportAllBtn = document.getElementById('device-export-all');
    const deviceSelection = new Set();

    const settingsForm = document.getElementById('settings-form');
    const settingsThemeSelect = document.getElementById('settings-theme');
    const settingsAccountNameInput = document.getElementById('settings-account-name');
    const settingsAccountEmailInput = document.getElementById('settings-account-email');
    const settingsEmailEnabled = document.getElementById('settings-email-enabled');
    const settingsEmailHost = document.getElementById('settings-email-host');
    const settingsEmailPort = document.getElementById('settings-email-port');
    const settingsEmailUsername = document.getElementById('settings-email-username');
    const settingsEmailPassword = document.getElementById('settings-email-password');
    const settingsEmailClear = document.getElementById('settings-email-clear');
    const settingsEmailPasswordNote = document.getElementById('settings-email-password-note');
    const settingsWebEnabled = document.getElementById('settings-web-enabled');
    const settingsStatus = document.getElementById('settings-status');

    const insightsSelect = document.getElementById('insights-device-select');
    const insightsRefreshBtn = document.getElementById('insights-refresh');
    const insightsEmpty = document.getElementById('insights-empty');
    const insightsContent = document.getElementById('insights-content');
    const insightsMeta = document.getElementById('insights-meta');
    const insightsChartCanvas = document.getElementById('insights-chart-canvas');
    const insightsLogsContainer = document.getElementById('insights-logs');

    const logsState = { initialized: false };
    const settingsState = { loaded: false, isLoading: false, passwordSet: false };
    const insightsState = { deviceId: null, isLoading: false };

    function normaliseKindValue(value){
      return (value || '').toString().trim().toLowerCase().replace(/[^a-z0-9]+/g, '_');
    }
    function resolveKindKey(value){
      const norm = normaliseKindValue(value);
      if(!norm){ return ''; }
      if(Object.prototype.hasOwnProperty.call(DEVICE_KIND_ALIASES, norm)){
        return DEVICE_KIND_ALIASES[norm];
      }
      return norm;
    }
    function formatKindLabel(value){
      if(!value){ return 'Device'; }
      return value.toString().replace(/[_\s]+/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
    }
    function getDeviceBadgeInfo(kind, platform){
      const candidates = [kind, platform].map(resolveKindKey).filter(Boolean);
      for(const key of candidates){
        if(Object.prototype.hasOwnProperty.call(DEVICE_KIND_META, key)){
          const base = DEVICE_KIND_META[key];
          return { ...base, label: base.label || formatKindLabel(kind || platform || base.label) };
        }
      }
      const fallbackLabel = formatKindLabel(kind || platform || DEVICE_KIND_META.default.label);
      return { ...DEVICE_KIND_META.default, label: fallbackLabel };
    }
    function createDeviceBadge(kind, platform){
      const info = getDeviceBadgeInfo(kind, platform);
      const badge = el('span',{class:`badge ${info.className}`});
      if(info.icon){
        badge.appendChild(el('span',{class:'badge-icon'}, info.icon));
      }
      badge.appendChild(el('span',{class:'badge-label'}, info.label));
      return badge;
    }

    function getDeviceTasks(device){
      const keys = [resolveKindKey(device?.kind), resolveKindKey(device?.platform)];
      const taskSet = new Set();
      keys.forEach(key => {
        if(key && Object.prototype.hasOwnProperty.call(DEVICE_TASKS, key)){
          DEVICE_TASKS[key].forEach(task => taskSet.add(task));
        }
      });
      if(taskSet.size === 0){
        DEVICE_TASKS.default.forEach(task => taskSet.add(task));
      }
      return Array.from(taskSet);
    }

    function setView(viewId){
      if(!viewId || !Object.prototype.hasOwnProperty.call(viewSections, viewId)){ return; }
      const target = viewSections[viewId];
      if(!target){ return; }
      Object.entries(viewSections).forEach(([key, section]) => {
        if(section){ section.classList.toggle('hidden', key !== viewId); }
      });
      navTabs.forEach(tab => {
        tab.classList.toggle('active', tab.dataset.view === viewId);
      });
      viewState.current = viewId;
      switch(viewId){
        case 'logs':
          ensureLogsInitialised();
          break;
        case 'devices':
          renderDeviceTable();
          break;
        case 'keys':
          ensureKeysLoaded();
          break;
        case 'settings':
          ensureSettingsLoaded();
          break;
        case 'insights':
          ensureInsightsReady();
          break;
        default:
          break;
      }
    }

    function ensureLogsInitialised(){
      if(logsState.initialized){ return; }
      logsState.initialized = true;
      refreshDeviceFilters();
      loadActivityLogs();
    }

    function refreshDeviceFilters(){
      if(!Array.isArray(devices) || devices.length === 0){
        if(logsDeviceKindSelect){
          logsDeviceKindSelect.innerHTML = '<option value="">All types</option>';
        }
        if(logsDeviceSelect){
          logsDeviceSelect.innerHTML = '<option value="">All devices</option>';
        }
        return;
      }
      if(logsDeviceKindSelect){
        const previous = logsDeviceKindSelect.value;
        const kinds = Array.from(new Set(devices
          .map(d => (d.kind || '').trim())
          .filter(Boolean)))
          .sort((a, b) => a.localeCompare(b));
        logsDeviceKindSelect.innerHTML = '<option value="">All types</option>';
        kinds.forEach(kind => {
          const option = document.createElement('option');
          option.value = kind;
          option.textContent = formatKindLabel(kind);
          logsDeviceKindSelect.appendChild(option);
        });
        if(previous && logsDeviceKindSelect.querySelector(`option[value="${previous}"]`)){
          logsDeviceKindSelect.value = previous;
        }
      }
      if(logsDeviceSelect){
        const previous = logsDeviceSelect.value;
        logsDeviceSelect.innerHTML = '<option value="">All devices</option>';
        devices.forEach(device => {
          const option = document.createElement('option');
          option.value = String(device.id);
          option.textContent = device.name || device.host || `Device #${device.id}`;
          logsDeviceSelect.appendChild(option);
        });
        if(previous && logsDeviceSelect.querySelector(`option[value="${previous}"]`)){
          logsDeviceSelect.value = previous;
        }
      }
    }

    async function loadActivityLogs(){
      if(!logsResults){ return; }
      const params = new URLSearchParams();
      params.set('limit', '200');
      const source = logsSourceSelect?.value || 'all';
      if(source && source !== 'all'){ params.set('source', source); }
      const deviceKind = logsDeviceKindSelect?.value?.trim();
      if(deviceKind){ params.set('device_kind', deviceKind); }
      const deviceId = logsDeviceSelect?.value?.trim();
      if(deviceId){ params.set('device_id', deviceId); }
      const level = logsLevelSelect?.value?.trim();
      if(level){ params.set('log_level', level); }
      const ipRange = logsIPInput?.value?.trim();
      if(ipRange){ params.set('ip_range', ipRange); }
      const search = logsSearchInput?.value?.trim();
      if(search){ params.set('q', search); }
      logsStatus.textContent = 'Loading activity…';
      logsResults.innerHTML = '';
      try {
        const data = await json(`/api/logs?${params.toString()}`);
        renderActivityLogList(Array.isArray(data) ? data : []);
      } catch(err){
        logsStatus.textContent = 'Failed to load activity: ' + err.message;
        logsResults.innerHTML = '<div class="empty-state">Unable to load logs.</div>';
      }
    }

    function renderActivityLogList(entries){
      if(!logsResults){ return; }
      if(!entries || entries.length === 0){
        logsResults.innerHTML = '<div class="empty-state">No events match the current filters.</div>';
        logsStatus.textContent = 'Showing 0 events.';
        return;
      }
      const fragment = document.createDocumentFragment();
      entries.forEach(entry => {
        const level = (entry.level || '').toString().toLowerCase();
        const levelPill = level ? el('span',{class:`log-level ${level}`}, level) : null;
        const source = (entry.source || '').toString().toLowerCase();
        const sourcePill = source ? el('span',{class:`log-source-pill ${source}`}, source === 'system' ? 'PulseOps' : 'Device') : null;
        const deviceBits = [];
        if(entry.device_name){ deviceBits.push(el('span',{class:'log-device'}, entry.device_name)); }
        if(entry.device_host){ deviceBits.push(el('span',{class:'muted'}, entry.device_host)); }
        const meta = el('div',{class:'log-meta'},
          el('span',{}, formatTimestamp(entry.timestamp || entry.ts)),
          sourcePill,
          levelPill,
          entry.category ? el('span',{class:'muted'}, entry.category) : null,
          ...deviceBits
        );
        const message = el('div',{class:'log-message'}, entry.message || '');
        const entryEl = el('div',{class:'log-entry'}, meta, message);
        if(entry.context && typeof entry.context === 'object'){
          const contextRow = el('div',{class:'log-context'});
          Object.entries(entry.context).forEach(([key,value]) => {
            contextRow.appendChild(el('span',{}, `${key}: ${value}`));
          });
          entryEl.appendChild(contextRow);
        }
        fragment.appendChild(entryEl);
      });
      logsResults.innerHTML = '';
      logsResults.appendChild(fragment);
      logsStatus.textContent = `Showing ${entries.length} event${entries.length === 1 ? '' : 's'}.`;
    }

    function renderDeviceTable(){
      if(!deviceTableBody){ return; }
      if(Array.isArray(devices)){
        const validIds = new Set(devices.map(d => d.id));
        Array.from(deviceSelection).forEach(id => {
          if(!validIds.has(id)){ deviceSelection.delete(id); }
        });
      }
      if(!Array.isArray(devices) || devices.length === 0){
        deviceTableBody.innerHTML = '<tr><td colspan="6" class="muted">No devices available.</td></tr>';
        updateDeviceSelectionUI();
        return;
      }
      const fragment = document.createDocumentFragment();
      devices.forEach(device => {
        const row = document.createElement('tr');
        const idStr = String(device.id);

        const selectCell = document.createElement('td');
        selectCell.style.width = '42px';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.dataset.deviceId = idStr;
        checkbox.checked = deviceSelection.has(device.id);
        checkbox.addEventListener('change', () => {
          if(checkbox.checked){
            deviceSelection.add(device.id);
          } else {
            deviceSelection.delete(device.id);
          }
          updateDeviceSelectionUI();
        });
        selectCell.appendChild(checkbox);

        const nameCell = document.createElement('td');
        nameCell.textContent = device.name || `Device #${device.id}`;

        const hostCell = document.createElement('td');
        hostCell.textContent = device.host || '—';

        const kindCell = document.createElement('td');
        kindCell.textContent = formatKindLabel(device.kind || '');

        const platformCell = document.createElement('td');
        platformCell.textContent = device.platform || '—';

        const userCell = document.createElement('td');
        userCell.textContent = device.user || '—';

        row.append(selectCell, nameCell, hostCell, kindCell, platformCell, userCell);
        fragment.appendChild(row);
      });
      deviceTableBody.innerHTML = '';
      deviceTableBody.appendChild(fragment);
      updateDeviceSelectionUI();
    }

    function updateDeviceSelectionUI(){
      if(deviceExportSelectedBtn){
        deviceExportSelectedBtn.disabled = deviceSelection.size === 0;
      }
      if(deviceTableMaster){
        if(!Array.isArray(devices) || devices.length === 0){
          deviceTableMaster.checked = false;
          deviceTableMaster.indeterminate = false;
        } else {
          let selectedCount = 0;
          devices.forEach(device => {
            if(deviceSelection.has(device.id)){ selectedCount += 1; }
          });
          deviceTableMaster.checked = selectedCount > 0 && selectedCount === devices.length;
          deviceTableMaster.indeterminate = selectedCount > 0 && selectedCount < devices.length;
        }
      }
    }

    function exportDevicesAsJSON(list, filename){
      if(!list || list.length === 0){
        showToast({ message: 'Select one or more devices to export.', duration: 4000, type: 'info' });
        return;
      }
      const timestamp = new Date().toISOString().replace(/[:]/g, '-').slice(0,19);
      const candidate = filename || `pulseops-devices-${timestamp}.json`;
      const safeName = candidate.replace(/[:]/g, '-');
      const blob = new Blob([JSON.stringify(list, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = safeName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      showToast({ message: `Exported ${list.length} device${list.length === 1 ? '' : 's'}.`, duration: 4000, type: 'success' });
    }

    async function ensureSettingsLoaded(){
      if(!settingsForm || settingsState.loaded || settingsState.isLoading){ return; }
      settingsState.isLoading = true;
      try {
        const data = await json('/api/settings');
        applySettingsToForm(data || {});
        settingsState.loaded = true;
      } catch(err){
        showToast({ message: 'Failed to load settings: ' + err.message, duration: 6000, type: 'error' });
      } finally {
        settingsState.isLoading = false;
      }
    }

    function applySettingsToForm(data){
      if(!settingsForm){ return; }
      if(settingsThemeSelect){
        const theme = data.theme || 'light';
        settingsThemeSelect.value = theme;
        // Apply theme immediately
        if(window.themeManager) {
          window.themeManager.setTheme(theme);
        }
      }
      if(settingsAccountNameInput){ settingsAccountNameInput.value = data.account_name || ''; }
      if(settingsAccountEmailInput){ settingsAccountEmailInput.value = data.account_email || ''; }
      if(settingsEmailEnabled){ settingsEmailEnabled.checked = Boolean(data.email_notifications_enabled); }
      if(settingsEmailHost){ settingsEmailHost.value = data.email_server_host || ''; }
      if(settingsEmailPort){ settingsEmailPort.value = data.email_server_port || 587; }
      if(settingsEmailUsername){ settingsEmailUsername.value = data.email_server_username || ''; }
      if(settingsEmailPassword){ settingsEmailPassword.value = ''; }
      if(settingsEmailClear){ settingsEmailClear.checked = false; }
      if(settingsEmailPasswordNote){
        const hasPassword = Boolean(data.email_server_password_set);
        settingsEmailPasswordNote.textContent = hasPassword ? 'A password is stored. Provide a new one to replace or tick "Clear".' : 'No password is currently stored.';
        settingsState.passwordSet = hasPassword;
      }
      if(settingsWebEnabled){ settingsWebEnabled.checked = Boolean(data.web_notifications_enabled); }
      updateEmailFieldState();
      if(settingsStatus){ settingsStatus.textContent = ''; }
    }

    function collectSettingsPayload(){
      const payload = {
        theme: settingsThemeSelect?.value || 'light',
        account_name: settingsAccountNameInput?.value?.trim() || '',
        account_email: settingsAccountEmailInput?.value?.trim() || '',
        email_notifications_enabled: !!settingsEmailEnabled?.checked,
        email_server_host: settingsEmailHost?.value?.trim() || '',
        email_server_port: Number(settingsEmailPort?.value) || 587,
        email_server_username: settingsEmailUsername?.value?.trim() || '',
        web_notifications_enabled: !!settingsWebEnabled?.checked
      };
      if(settingsEmailClear?.checked){
        payload.email_server_password = '';
      } else if(settingsEmailPassword){
        const pwd = settingsEmailPassword.value.trim();
        if(pwd){ payload.email_server_password = pwd; }
      }
      return payload;
    }

    function updateEmailFieldState(){
      const disabled = !settingsEmailEnabled?.checked;
      [settingsEmailHost, settingsEmailPort, settingsEmailUsername, settingsEmailPassword, settingsEmailClear]
        .filter(Boolean)
        .forEach(input => {
          input.disabled = disabled;
        });
      if(settingsEmailPasswordNote){
        settingsEmailPasswordNote.classList.toggle('muted', !settingsEmailEnabled?.checked);
      }
    }

    function ensureInsightsReady(){
      refreshInsightsSelector();
      if(insightsState.deviceId){
        renderInsightsForDevice(insightsState.deviceId);
      } else {
        showInsightsEmpty();
      }
    }

    function refreshInsightsSelector(){
      if(!insightsSelect){ return; }
      const previous = insightsSelect.value;
      insightsSelect.innerHTML = '';
      const placeholder = document.createElement('option');
      placeholder.value = '';
      placeholder.textContent = 'Select a device…';
      insightsSelect.appendChild(placeholder);
      devices.forEach(device => {
        const option = document.createElement('option');
        option.value = String(device.id);
        option.textContent = device.name || device.host || `Device #${device.id}`;
        insightsSelect.appendChild(option);
      });
      if(previous && insightsSelect.querySelector(`option[value="${previous}"]`)){
        insightsSelect.value = previous;
        insightsState.deviceId = Number(previous);
      } else if(!insightsSelect.value){
        insightsState.deviceId = null;
      }
      if(insightsRefreshBtn){ insightsRefreshBtn.disabled = !insightsSelect.value; }
    }

    function showInsightsEmpty(message){
      if(insightsEmpty){
        insightsEmpty.textContent = message || 'Pick a device to inspect historical metrics and activity.';
        insightsEmpty.classList.remove('hidden');
      }
      if(insightsContent){ insightsContent.classList.add('hidden'); }
    }

    async function renderInsightsForDevice(deviceId){
      if(!deviceId || !insightsMeta || !insightsChartCanvas){
        showInsightsEmpty();
        return;
      }
      const device = devices.find(d => d.id === deviceId);
      if(!device){
        showInsightsEmpty('Device not found.');
        return;
      }
      insightsState.deviceId = deviceId;
      if(insightsRefreshBtn){ insightsRefreshBtn.disabled = false; }
      if(insightsEmpty){
        insightsEmpty.textContent = 'Loading device insights…';
        insightsEmpty.classList.remove('hidden');
      }
      insightsContent?.classList.add('hidden');
      try {
        insightsMeta.innerHTML = '';
        const metaBits = [
          `Host: ${device.host || 'n/a'}`,
          `Kind: ${formatKindLabel(device.kind || '')}`,
          `Platform: ${device.platform || 'n/a'}`,
          `User: ${device.user || 'n/a'}`
        ];
        metaBits.forEach(text => {
          insightsMeta.appendChild(el('span',{}, text));
        });
        await Promise.all([
          renderInsightsChart(deviceId),
          renderInsightsLogs(deviceId)
        ]);
        insightsContent?.classList.remove('hidden');
        insightsEmpty?.classList.add('hidden');
        insightsEmpty.textContent = 'Pick a device to inspect historical metrics and activity.';
      } catch(err){
        showInsightsEmpty('Failed to load device insights: ' + err.message);
      }
    }

    async function renderInsightsChart(deviceId){
      if(!insightsChartCanvas){ return; }
      const since = new Date(Date.now() - 24 * 3600e3).toISOString();
      const series = await Promise.all(METRIC_SERIES.map(async def => {
        try {
          const rows = await json(`/api/metrics?device_id=${deviceId}&metric=${def.key}&since=${encodeURIComponent(since)}&limit=1440`);
          const points = (rows || []).map(row => {
            const tsRaw = row.ts || row.TS || row.timestamp;
            const value = extractMetricValue(row);
            const ts = tsRaw ? new Date(tsRaw).getTime() : NaN;
            return { ts, value };
          }).filter(point => Number.isFinite(point.ts) && typeof point.value === 'number' && Number.isFinite(point.value));
          return { ...def, points };
        } catch(err){
          return { ...def, points: [] };
        }
      }));
      drawLineChart(insightsChartCanvas, series);
    }

    async function renderInsightsLogs(deviceId){
      if(!insightsLogsContainer){ return; }
      insightsLogsContainer.innerHTML = '<div class="muted">Loading activity…</div>';
      try {
        const logs = await json(`/api/device-logs?device_id=${deviceId}&limit=25`);
        if(!Array.isArray(logs) || logs.length === 0){
          insightsLogsContainer.innerHTML = '<div class="muted">No recent activity.</div>';
          return;
        }
        const fragment = document.createDocumentFragment();
        logs.forEach(entry => {
          const level = (entry.level || '').toString().toLowerCase();
          const meta = el('div',{class:'log-meta'},
            el('span',{}, formatTimestamp(entry.ts)),
            el('span',{class:`log-level ${level}`}, level || 'info')
          );
          const body = el('div',{class:'log-message'}, entry.message || '');
          fragment.appendChild(el('div',{class:'log-entry'}, meta, body));
        });
        insightsLogsContainer.innerHTML = '';
        insightsLogsContainer.appendChild(fragment);
      } catch(err){
        insightsLogsContainer.innerHTML = `<div class="muted">Failed to load activity: ${escapeHTML(err.message)}</div>`;
      }
    }

    function clearCountdowns(){
      for(const handle of countdownIntervals.values()){
        clearInterval(handle);
      }
      countdownIntervals.clear();
    }

    function setupCountdown(id, target, statusEl){
      if(!target){ return; }
      const deadline = new Date(target).getTime();
      const update = () => {
        const remaining = deadline - Date.now();
        if (remaining <= 0) {
          statusEl.textContent = 'Deleting...';
          const handle = countdownIntervals.get(id);
          if(handle){ clearInterval(handle); countdownIntervals.delete(id); }
          return;
        }
        const seconds = Math.ceil(remaining / 1000);
        statusEl.textContent = `Deleting in ${seconds}s`;
      };
      update();
      const interval = setInterval(update, 1000);
      countdownIntervals.set(id, interval);
    }

    function goToInsights(deviceId){
      setView('insights');
      if(insightsSelect){
        insightsSelect.value = String(deviceId);
      }
      insightsState.deviceId = Number(deviceId);
      if(insightsRefreshBtn){ insightsRefreshBtn.disabled = false; }
      renderInsightsForDevice(Number(deviceId));
      try { window.scrollTo({ top: 0, behavior: 'smooth' }); } catch(_) {}
    }

    function deviceCard(d){
      const badge = createDeviceBadge(d.kind, d.platform);
      const nameLabel = el('strong',{class:'device-name'}, d.name || 'Unnamed device');
      const titleRow = el('div',{class:'device-title-row'}, badge, nameLabel);
      const subtitleText = `${d.kind || 'Unknown'} / ${d.platform || 'Unknown'}`;
      const headerInfo = el('div',{class:'device-header-info'},
        titleRow,
        el('div',{class:'muted device-subtitle'}, subtitleText)
      );
      const btnReboot = el('button',{onClick:()=>promptTaskConfirmation(d,'reboot')},'Reboot');
      const btnFW = el('button',{onClick:()=>promptTaskConfirmation(d,'refresh_firewall')},'Refresh firewall');
      const btnWi = el('button',{onClick:()=>promptTaskConfirmation(d,'refresh_wireless')},'Refresh wireless');
      const btnUndo = el('button',{onClick:()=>restoreDevice(d.id)},'Undo delete');
      const menu = createDeviceMenu(d);
      const btnInsights = el('button',{onClick:()=>goToInsights(d.id)},'View insights');

      const latest = el('div',{class:'latest-metrics'},
        el('div',{class:'metrics-row metrics-header'},
          el('div',{class:'metric-cell'}, 'Ping (24h avg)'),
          el('div',{class:'metric-cell'}, 'Bandwidth (24h avg)')
        ),
        el('div',{class:'metrics-row metrics-values'},
          el('div',{class:'metric-cell', id:`m-ping-${d.id}`}, d.pending_delete_at ? '--' : '--'),
          el('div',{class:'metric-cell', id:`m-iperf-${d.id}`}, '--')
        )
      );
      const logs = el('div',{class:'device-logs'},
        el('div',{class:'device-log-header'},
          el('span',{},'Activity'),
          el('button',{class:'device-log-toggle', type:'button', id:`logs-toggle-${d.id}`},'See more')
        ),
        el('div',{class:'device-log-list', id:`logs-${d.id}`},
          el('div',{class:'muted'}, d.pending_delete_at ? 'Logs unavailable while deletion is pending.' : 'Loading activity...')
        )
      );
      const status = el('div',{class:'muted', id:`status-${d.id}`});

      const buttonByTask = {
        reboot: btnReboot,
        refresh_firewall: btnFW,
        refresh_wireless: btnWi
      };
      const availableTasks = getDeviceTasks(d);
      const actionButtons = [];
      availableTasks.forEach(task => {
        const btn = buttonByTask[task];
        if(btn){ actionButtons.push(btn); }
      });
      if(d.pending_delete_at){
        actionButtons.forEach(btn => { btn.disabled = true; });
        actionButtons.push(btnUndo);
      }
      actionButtons.push(btnInsights);
      const actions = el('div',{class:'row'}, actionButtons);

      const chart = el('canvas',{id:`c-${d.id}`, width:'320', height:'140'});
      const chartSection = el('div',{class:'metric-chart'}, chart);

      const taskSection = el('div',{class:'task-section'},
        el('h4',{},'Recent tasks'),
        el('div',{class:'task-list', id:`tasks-${d.id}`}, el('div',{class:'muted'},'Loading tasks...'))
      );

      const card = el('div',{class:'card'},
        el('div',{class:'row device-header'}, headerInfo, menu),
        el('div',{class:'mono'}, d.host),
        el('div',{style:'height:.4rem'}),
        latest,
        logs,
        status,
        chartSection,
        el('div',{style:'height:.6rem'}),
        actions,
        taskSection
      );

      if(d.pending_delete_at){
        setupCountdown(d.id, d.pending_delete_at, status);
      } else {
        status.textContent = '';
      }

      return card;
    }

    function createDeviceMenu(device){
      const menuList = el('div',{class:'menu-list hidden', role:'menu'});
      const insightsItem = el('button',{type:'button', class:'menu-item', onClick:(evt)=>{
        evt.stopPropagation();
        closeOpenMenu();
        goToInsights(device.id);
      }},'Insights');
      menuList.appendChild(insightsItem);
      const editItem = el('button',{type:'button', class:'menu-item', onClick:(evt)=>{
        evt.stopPropagation();
        closeOpenMenu();
        editDevice(device.id);
      }},'Edit');
      menuList.appendChild(editItem);
      if(!device.pending_delete_at){
        const deleteItem = el('button',{type:'button', class:'menu-item danger', onClick:(evt)=>{
          evt.stopPropagation();
          closeOpenMenu();
          promptDeleteDevice(device.id);
        }},'Delete');
        menuList.appendChild(deleteItem);
      }
      const trigger = el('button',{class:'menu-trigger', type:'button', 'aria-haspopup':'true', 'aria-expanded':'false', 'aria-label':'Device options'}, '⋯');
      trigger.addEventListener('click',(evt)=>{
        evt.stopPropagation();
        toggleDeviceMenu(trigger, menuList);
      });
      const wrapper = el('div',{class:'menu-wrapper'}, trigger, menuList);
      return wrapper;
    }

    async function refreshLatest(d){
      if(d.pending_delete_at){ return; }

      const pingEl = document.getElementById(`m-ping-${d.id}`);
      const iperfEl = document.getElementById(`m-iperf-${d.id}`);
      const since = new Date(Date.now() - 24 * 3600e3).toISOString();

      if(pingEl){
        try {
          const avg = await fetchMetricAverage(d.id, 'ping_ms', since);
          if(avg != null){
            pingEl.textContent = `${avg.toFixed(1)} ms`;
          } else {
            pingEl.textContent = 'n/a';
          }
        } catch(e){
          pingEl.textContent = 'n/a';
        }
      }

      if(iperfEl){
        try {
          const avg = await fetchMetricAverage(d.id, 'iperf_mbps', since);
          if(avg != null){
            iperfEl.textContent = `${avg.toFixed(1)} Mbps`;
          } else {
            iperfEl.textContent = 'n/a';
          }
        } catch(e){
          iperfEl.textContent = 'n/a';
        }
      }
    }

    async function runTask(device_id, kind){
      const label = TASK_LABELS[kind] || kind;
      try {
        const res = await json('/api/tasks', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({device_id, kind, by:'web'})});
        showToast({ message: `${label} queued (#${res.enqueued}).`, duration: 5000, type: 'success' });
        await loadDeviceTasks(device_id);
      } catch(e){
        console.error('Failed to enqueue task', e);
        showToast({ message: `Failed to enqueue ${label}: ${e.message}`, duration: 6000, type: 'error' });
      }
    }

    function renderDeviceLogs(container, logs, expanded){
      if(!logs || logs.length === 0){
        container.innerHTML = '<div class="muted">No recent activity.</div>';
        return;
      }
      const fragment = document.createDocumentFragment();
      const limit = expanded ? logs.length : 1;
      logs.slice(0, limit).forEach(entry => {
        const timeText = formatLogTime(entry.ts);
        const level = (entry.level || 'info').slice(0, 12);
        const message = truncateText(entry.message || '', 160);
        const levelSpan = el('span',{class:'device-log-level'}, level);
        const messageSpan = el('span',{class:'device-log-message'}, message);
        const row = el('div',{class:'device-log-entry'},
          el('div',{class:'device-log-time'}, timeText),
          el('div',{class:'device-log-body'}, levelSpan, messageSpan)
        );
        fragment.appendChild(row);
      });
      container.innerHTML = '';
      container.appendChild(fragment);
    }

    async function loadDeviceLogs(deviceId){
      const container = document.getElementById(`logs-${deviceId}`);
      const toggle = document.getElementById(`logs-toggle-${deviceId}`);
      if(!container){ return; }
      try {
        const logs = await json(`/api/device-logs?device_id=${deviceId}&limit=5`);
        if(Array.isArray(logs)){
          const expanded = logExpansionState.get(deviceId) || false;
          renderDeviceLogs(container, logs, expanded);
          if(toggle){
            toggle.textContent = expanded ? 'See less' : 'See more';
          }
        } else {
          renderDeviceLogs(container, [], false);
          if(toggle){ toggle.textContent = 'See more'; }
        }
      } catch(err){
        container.innerHTML = '<div class="muted">Unable to load activity.</div>';
        if(toggle){ toggle.textContent = 'See more'; }
      }
    }

    function showToast(options){
      if(!toastContainer){ return { dismiss: () => {} }; }
      const { message, actionText, onAction, dismissText = 'Dismiss', countdownSeconds, type } = options;
      const hasCountdown = typeof countdownSeconds === 'number' && countdownSeconds > 0;
      const duration = options.duration ?? (hasCountdown ? countdownSeconds * 1000 : 5000);
      const toast = el('div',{class:'toast'});
      if(type){ toast.classList.add(type); }
      const messageSpan = el('span',{});
      toast.appendChild(messageSpan);
      let remainingSeconds = hasCountdown ? Math.round(countdownSeconds) : 0;
      const isMessageFn = typeof message === 'function';
      function renderMessage(){
        if(isMessageFn){
          messageSpan.textContent = message(Math.max(0, remainingSeconds));
        } else if(hasCountdown){
          messageSpan.textContent = `${message} (${Math.max(0, remainingSeconds)}s)`;
        } else {
          messageSpan.textContent = message;
        }
      }
      renderMessage();
      let countdownInterval;
      if(hasCountdown){
        countdownInterval = setInterval(() => {
          remainingSeconds -= 1;
          if(remainingSeconds <= 0){
            remainingSeconds = 0;
            renderMessage();
            clearInterval(countdownInterval);
            countdownInterval = undefined;
          } else {
            renderMessage();
          }
        }, 1000);
      }
      let dismissed = false;
      function dismiss(){
        if(dismissed){ return; }
        dismissed = true;
        clearTimeout(timeoutId);
        if(countdownInterval){ clearInterval(countdownInterval); }
        toast.remove();
      }
      if(actionText && typeof onAction === 'function'){
        const actionBtn = el('button',{type:'button'}, actionText);
        actionBtn.addEventListener('click', async (evt) => {
          evt.stopPropagation();
          try {
            const result = await onAction(dismiss);
            if(result !== false){ dismiss(); }
          } catch(err){
            console.error('Toast action failed', err);
          }
        });
        toast.appendChild(actionBtn);
      }
      const dismissBtn = el('button',{type:'button'}, dismissText);
      dismissBtn.addEventListener('click', (evt) => {
        evt.stopPropagation();
        dismiss();
      });
      toast.appendChild(dismissBtn);
      toastContainer.appendChild(toast);
      const timeoutId = setTimeout(dismiss, duration);
      return { dismiss, renderMessage };
    }

    function getTaskStatusClass(status){
      switch((status || '').toLowerCase()){
        case 'queued': return 'status-queued';
        case 'running': return 'status-running';
        case 'done': return 'status-done';
        case 'error': return 'status-error';
        default: return 'status-queued';
      }
    }

    function formatTaskStatus(status){
      switch((status || '').toLowerCase()){
        case 'queued': return 'Queued';
        case 'running': return 'Running';
        case 'done': return 'Completed';
        case 'error': return 'Error';
        default: return status || 'Unknown';
      }
    }

    function formatTaskOutput(output){
      if(!output){ return ''; }
      const trimmed = output.trim();
      if(!trimmed){ return ''; }
      const lines = trimmed.split(/\r?\n/).filter(Boolean);
      if(lines.length > 3){
        return lines.slice(-3).join('\n');
      }
      return lines.join('\n');
    }

    function clearTaskPoller(deviceId){
      const existing = taskRefreshTimers.get(deviceId);
      if(existing){
        clearTimeout(existing);
        taskRefreshTimers.delete(deviceId);
      }
    }

    function scheduleTaskRefresh(deviceId, tasks){
      clearTaskPoller(deviceId);
      if(Array.isArray(tasks) && tasks.some(t => {
        const status = (t.status || '').toLowerCase();
        return status === 'queued' || status === 'running';
      })){
        const timer = setTimeout(() => {
          loadDeviceTasks(deviceId);
        }, 5000);
        taskRefreshTimers.set(deviceId, timer);
      }
    }

    const taskLoadErrors = new Set();

    async function loadDeviceTasks(deviceId){
      try {
        const list = await json(`/api/tasks?device_id=${deviceId}`);
        deviceTasks.set(deviceId, list);
        taskLoadErrors.delete(deviceId);
        renderTaskList(deviceId);
        scheduleTaskRefresh(deviceId, list);
      } catch(err){
        console.error(`Failed to load tasks for ${deviceId}`, err);
        const container = document.getElementById(`tasks-${deviceId}`);
        if(container){ container.innerHTML = '<div class="muted">Failed to load tasks.</div>'; }
        if(!taskLoadErrors.has(deviceId)){
          taskLoadErrors.add(deviceId);
          showToast({ message: `Failed to load tasks: ${err.message}`, duration: 6000, type: 'error' });
        }
      }
    }

    function renderTaskList(deviceId){
      const container = document.getElementById(`tasks-${deviceId}`);
      if(!container){ return; }
      const tasks = deviceTasks.get(deviceId) || [];
      if(tasks.length === 0){
        container.innerHTML = '<div class="muted">No tasks yet.</div>';
        return;
      }
      if(tasks.length <= TASK_PREVIEW_LIMIT && expandedTaskPanels.has(deviceId)){
        expandedTaskPanels.delete(deviceId);
      }
      const isExpanded = expandedTaskPanels.has(deviceId);
      const limit = isExpanded ? TASK_EXPANDED_LIMIT : TASK_PREVIEW_LIMIT;
      const visible = tasks.slice(0, limit);
      container.innerHTML = '';
      visible.forEach(task => {
        const statusClass = getTaskStatusClass(task.status);
        const statusLabel = formatTaskStatus(task.status);
        const title = `${TASK_LABELS[task.kind] || task.kind}`;
        const requestedAt = formatTimestamp(task.requested_at);
        const item = document.createElement('div');
        item.className = 'task-item';
        const meta = document.createElement('div');
        meta.className = 'task-meta';
        const metaText = document.createElement('div');
        metaText.innerHTML = `<strong>${escapeHTML(title)}</strong><div class="muted" style="font-size:.8rem;">${escapeHTML(requestedAt)}</div>`;
        const statusPill = document.createElement('span');
        statusPill.className = `status-pill ${statusClass}`;
        statusPill.textContent = statusLabel;
        meta.appendChild(metaText);
        meta.appendChild(statusPill);
        item.appendChild(meta);
        const snippet = formatTaskOutput(task.output);
        if(snippet){
          const outputEl = document.createElement('div');
          outputEl.className = 'task-output';
          outputEl.textContent = snippet;
          item.appendChild(outputEl);
        }
        container.appendChild(item);
      });
      if(tasks.length > visible.length){
        const toggle = document.createElement('button');
        toggle.type = 'button';
        toggle.className = 'task-toggle';
        toggle.textContent = isExpanded ? 'Show less' : `Show more (${tasks.length - visible.length} more)`;
        toggle.addEventListener('click', () => toggleTaskList(deviceId));
        container.appendChild(toggle);
      } else if(isExpanded && tasks.length > TASK_PREVIEW_LIMIT){
        const toggle = document.createElement('button');
        toggle.type = 'button';
        toggle.className = 'task-toggle';
        toggle.textContent = 'Show less';
        toggle.addEventListener('click', () => toggleTaskList(deviceId));
        container.appendChild(toggle);
      }
    }

    function toggleTaskList(deviceId){
      if(expandedTaskPanels.has(deviceId)){
        expandedTaskPanels.delete(deviceId);
      } else {
        expandedTaskPanels.add(deviceId);
      }
      renderTaskList(deviceId);
    }

    function setButtonVariant(button, variant){
      if(!button){ return; }
      button.className = 'btn';
      switch(variant){
        case 'danger':
          button.classList.add('btn-danger');
          break;
        case 'secondary':
          button.classList.add('btn-secondary');
          break;
        case 'outline':
          button.classList.add('btn-outline');
          break;
        default:
          button.classList.add('btn-primary');
      }
    }

    function clearConfirmExtraButtons(){
      if(confirmExtraContainer){ confirmExtraContainer.innerHTML = ''; }
      confirmState.extraButtons = [];
    }

    function addConfirmExtraAction(action){
      if(!confirmExtraContainer){ return; }
      const btn = document.createElement('button');
      btn.type = 'button';
      setButtonVariant(btn, action?.variant || 'outline');
      btn.textContent = action?.label || 'Confirm';
      btn.addEventListener('click', async () => {
        if(action?.close !== false){ closeConfirmDialog(); }
        if(typeof action?.onConfirm === 'function'){
          try {
            await action.onConfirm();
          } catch(err){
            console.error('Confirm action failed', err);
          }
        }
      });
      confirmExtraContainer.appendChild(btn);
      confirmState.extraButtons.push(btn);
    }

    function openConfirmDialog({ title, message, confirmText, variant = 'primary', onConfirm, extraActions = [], renderExtra }){
      if(!confirmOverlay){
        if(typeof onConfirm === 'function'){ onConfirm(); }
        return;
      }
      confirmTitleEl.textContent = title || 'Confirm action';
      confirmMessageEl.textContent = message || 'Are you sure you want to continue?';
      confirmConfirmBtn.textContent = confirmText || 'Confirm';
      setButtonVariant(confirmConfirmBtn, variant);
      confirmState.onConfirm = typeof onConfirm === 'function' ? onConfirm : null;
      clearConfirmExtraButtons();
      if(typeof renderExtra === 'function'){
        renderExtra(confirmExtraContainer);
      } else if(Array.isArray(extraActions)){
        extraActions.forEach(addConfirmExtraAction);
      }
      confirmOverlay.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
      if(confirmConfirmBtn && typeof confirmConfirmBtn.focus === 'function'){
        try { confirmConfirmBtn.focus({ preventScroll: true }); } catch(_) { confirmConfirmBtn.focus(); }
      }
    }

    function closeConfirmDialog(){
      if(!confirmOverlay || confirmOverlay.classList.contains('hidden')){ return; }
      confirmOverlay.classList.add('hidden');
      confirmState.onConfirm = null;
      clearConfirmExtraButtons();
      if(!(editOverlay && editState.open) && !(editSSHModal && editSSHModal.classList.contains('active'))){
        document.body.style.overflow = '';
      }
    }

    function resetEditState(){
      editState.step = EDIT_STEPS.CONFIG;
      editState.deviceId = null;
      editState.template = null;
      editState.deviceConfig = { meta: {} };
      editState.validation = null;
      editState.isValidating = false;
      editState.isSaving = false;
    }

    function setEditLoading(isLoading, message){
      if(!editOverlay){ return; }
      if(isLoading){
        editLoadingState?.classList.remove('hidden');
        editContent?.classList.add('hidden');
      } else {
        editLoadingState?.classList.add('hidden');
        editContent?.classList.remove('hidden');
      }
      if(message && editLoadingState){
        const text = editLoadingState.querySelector('p');
        if(text){ text.textContent = message; }
      }
    }

    function closeEditOverlay(){
      if(!editOverlay){ return; }
      editOverlay.classList.remove('active');
      editState.open = false;
      closeEditSSHKeyManager();
      if(!(confirmOverlay && !confirmOverlay.classList.contains('hidden')) && !(editSSHModal && editSSHModal.classList.contains('active'))){
        document.body.style.overflow = '';
      }
      resetEditState();
      if(editForm){ editForm.innerHTML = ''; }
      editValidationResults?.classList.add('hidden');
      editSummary?.classList.add('hidden');
      editValidationResults.innerHTML = '';
      editSummary.innerHTML = '';
      setEditLoading(true);
    }

    function openEditOverlay(){
      if(!editOverlay){ return; }
      editOverlay.classList.add('active');
      document.body.style.overflow = 'hidden';
      editState.open = true;
      setEditStep(EDIT_STEPS.CONFIG);
    }

    function setEditStep(step){
      editState.step = step;
      if(!editOverlay){ return; }
      const stepElems = editOverlay.querySelectorAll('.edit-step');
      stepElems.forEach((el, idx) => {
        el.classList.toggle('active', idx === step - 1);
      });
      const stepperItems = editOverlay.querySelectorAll('.stepper-item');
      stepperItems.forEach(item => {
        const itemStep = Number(item.getAttribute('data-edit-step'));
        item.classList.toggle('active', itemStep === step);
        item.classList.toggle('completed', itemStep < step);
      });
      if(editBackBtn){ editBackBtn.textContent = step === EDIT_STEPS.CONFIG ? 'Cancel' : 'Back'; }
      if(editValidationResults){ editValidationResults.classList.toggle('hidden', step === EDIT_STEPS.CONFIG); }
      if(editSummary){ editSummary.classList.toggle('hidden', step === EDIT_STEPS.CONFIG); }
      setEditActionState();
    }

    function setEditActionState(){
      const disabled = editState.isValidating || editState.isSaving;
      if(editValidateBtn){ editValidateBtn.disabled = disabled; }
      if(editSaveBtn){ editSaveBtn.disabled = disabled; }
      if(editBackBtn){ editBackBtn.disabled = editState.isValidating; }
    }

    async function ensureTemplatesLoaded(){
      if(templatesCache){ return templatesCache; }
      templatesCache = await json('/api/templates');
      return templatesCache;
    }

    async function ensureSSHKeysLoaded(forceReload=false){
      if(!forceReload && sshKeysCache.length){ return sshKeysCache; }
      try {
        sshKeysCache = await json('/api/ssh-keys');
      } catch(err){
        console.error('Failed to load SSH keys', err);
        sshKeysCache = [];
        if(!sshKeysLoadErrorNotified){
          showToast({ message: 'SSH key manager unavailable. Provide a filesystem path manually.', duration: 6000 });
          sshKeysLoadErrorNotified = true;
        }
      }
      return sshKeysCache;
    }

    function renderEditForm(){
      if(!editForm || !editState.template){ return; }
      const template = editState.template;
      const cfg = editState.deviceConfig || { meta: {} };
      editForm.innerHTML = template.fields.map(field => {
        const currentValue = cfg[field.name] !== undefined ? cfg[field.name] : (field.default ?? '');
        if(field.name === 'ssh_key'){
          return `
            <div class="form-group" id="edit-ssh-key-group">
              <label>${escapeHTML(field.label)}${field.required ? ' *' : ''}</label>
              <div class="ssh-key-field-controls">
                <select id="edit-ssh-key-select" ${field.required ? 'required' : ''}></select>
                <button type="button" class="btn btn-secondary" id="edit-manage-ssh-btn">Manage Keys</button>
              </div>
              <input type="text" id="edit-ssh-key-path" class="hidden" placeholder="${escapeHTML(field.placeholder || '')}">
              <input type="hidden" name="${escapeHTML(field.name)}" id="edit-ssh-key-hidden" value="${escapeHTML(currentValue)}">
              ${field.help ? `<div class="help">${escapeHTML(field.help)}</div>` : ''}
            </div>
          `;
        }
        let input = '';
        const escapedValue = escapeHTML(currentValue);
        switch(field.type){
          case 'select':
            input = `
              <select name="${escapeHTML(field.name)}" ${field.required ? 'required' : ''}>
                <option value="">Choose...</option>
                ${field.options.map(opt => `<option value="${escapeHTML(opt)}" ${currentValue === opt ? 'selected' : ''}>${escapeHTML(opt)}</option>`).join('')}
              </select>
            `;
            break;
          case 'textarea':
            input = `<textarea name="${escapeHTML(field.name)}" placeholder="${escapeHTML(field.placeholder || '')}" ${field.required ? 'required' : ''}>${escapedValue}</textarea>`;
            break;
          case 'password':
            input = `<input type="password" name="${escapeHTML(field.name)}" placeholder="${escapeHTML(field.placeholder || '')}" value="${escapedValue}" ${field.required ? 'required' : ''}>`;
            break;
          case 'number':
            input = `<input type="number" name="${escapeHTML(field.name)}" placeholder="${escapeHTML(field.placeholder || '')}" value="${escapedValue}" ${field.required ? 'required' : ''}>`;
            break;
          default:
            input = `<input type="text" name="${escapeHTML(field.name)}" placeholder="${escapeHTML(field.placeholder || '')}" value="${escapedValue}" ${field.required ? 'required' : ''}>`;
        }
        return `
          <div class="form-group" data-field="${escapeHTML(field.name)}">
            <label>${escapeHTML(field.label)}${field.required ? ' *' : ''}</label>
            ${input}
            ${field.help ? `<div class="help">${escapeHTML(field.help)}</div>` : ''}
          </div>
        `;
      }).join('');

      const sshField = template.fields.find(field => field.name === 'ssh_key');
      if(sshField){
        initializeEditSSHField(cfg['ssh_key'] ?? sshField.default ?? '', sshField.placeholder);
      }
    }

    function showEditFieldError(fieldName, message){
      if(!editForm){ return; }
      const field = editForm.querySelector(`[name="${CSS.escape(fieldName)}"]`);
      if(field){
        const error = document.createElement('div');
        error.className = 'error';
        error.textContent = message;
        field.parentNode.appendChild(error);
      }
    }

    function validateEditForm(){
      if(!editForm || !editState.template){ return false; }
      const formData = new FormData(editForm);
      editForm.querySelectorAll('.error').forEach(el => el.remove());
      let isValid = true;
      const cfg = editState.deviceConfig;
      cfg.meta = cfg.meta || {};

      editState.template.fields.forEach(field => {
        const rawValue = formData.get(field.name);
        const value = typeof rawValue === 'string' ? rawValue.trim() : rawValue;
        if(field.required && (!value || value === '')){
          showEditFieldError(field.name, `${field.label} is required`);
          isValid = false;
        }

        if(field.name === 'ssh_port'){
          if(value === '' || value === undefined){
            delete cfg.meta.ssh_port;
            cfg.ssh_port = '';
          } else {
            const port = parseInt(value, 10);
            if(Number.isNaN(port) || port <= 0 || port > 65535){
              showEditFieldError(field.name, 'Enter a valid SSH port between 1 and 65535');
              isValid = false;
            } else {
              cfg.meta.ssh_port = String(port);
              cfg.ssh_port = String(port);
            }
          }
        } else if(field.name === 'ssh_key'){
          cfg[field.name] = value || '';
        } else if(field.name in cfg || value !== null){
          cfg[field.name] = value ?? '';
        }
      });

      cfg.kind = editState.template.kind;
      cfg.platform = editState.template.platform;

      return isValid;
    }

    function buildEditPayload(){
      const payload = { ...editState.deviceConfig };
      const meta = { ...(payload.meta || {}) };
      if(meta.ssh_port === '' || meta.ssh_port === undefined){
        delete meta.ssh_port;
      }
      if(Object.keys(meta).length > 0){
        payload.meta = meta;
      } else {
        delete payload.meta;
      }
      if(Object.prototype.hasOwnProperty.call(payload, 'ssh_port')){
        delete payload.ssh_port;
      }
      return payload;
    }

    function refreshEditSSHKeySelect(preservedValue){
      const select = document.getElementById('edit-ssh-key-select');
      const hidden = document.getElementById('edit-ssh-key-hidden');
      if(!select || !hidden){ return; }
      const currentValue = preservedValue !== undefined ? preservedValue : hidden.value;
      const existing = new Set();
      select.innerHTML = '';

      const placeholder = document.createElement('option');
      placeholder.value = '';
      placeholder.textContent = sshKeysCache.length ? 'Select a saved key' : 'No saved keys available';
      select.appendChild(placeholder);
      existing.add('');

      sshKeysCache.forEach(key => {
        const reference = `${SSH_KEY_REFERENCE_PREFIX}${key.id}`;
        const option = document.createElement('option');
        option.value = reference;
        option.textContent = `${key.name} (${key.fingerprint})`;
        if(reference === currentValue){ option.selected = true; }
        select.appendChild(option);
        existing.add(reference);
      });

      const pathOption = document.createElement('option');
      pathOption.value = SSH_KEY_PATH_OPTION;
      pathOption.textContent = 'Use filesystem path';
      select.appendChild(pathOption);
      existing.add(SSH_KEY_PATH_OPTION);

      if(currentValue && currentValue.startsWith(SSH_KEY_REFERENCE_PREFIX) && !existing.has(currentValue)){
        const missing = document.createElement('option');
        missing.value = currentValue;
        missing.textContent = `Stored key ${currentValue.replace(SSH_KEY_REFERENCE_PREFIX, '#')}`;
        missing.selected = true;
        select.appendChild(missing);
      }

      setEditSSHKeySelection(currentValue);
    }

    function setEditSSHKeySelection(value){
      const select = document.getElementById('edit-ssh-key-select');
      const hidden = document.getElementById('edit-ssh-key-hidden');
      const pathInput = document.getElementById('edit-ssh-key-path');
      if(!select || !hidden || !pathInput){ return; }

      if(value && value.startsWith(SSH_KEY_REFERENCE_PREFIX)){
        select.value = value;
        hidden.value = value;
        pathInput.classList.add('hidden');
        pathInput.value = '';
      } else if(value){
        select.value = SSH_KEY_PATH_OPTION;
        hidden.value = value;
        pathInput.classList.remove('hidden');
        pathInput.value = value;
      } else {
        select.value = '';
        hidden.value = '';
        pathInput.classList.add('hidden');
        pathInput.value = '';
      }
    }

    function initializeEditSSHField(initialValue, placeholder){
      const select = document.getElementById('edit-ssh-key-select');
      const hidden = document.getElementById('edit-ssh-key-hidden');
      const pathInput = document.getElementById('edit-ssh-key-path');
      const managerBtn = document.getElementById('edit-manage-ssh-btn');
      if(!select || !hidden || !pathInput){ return; }
      if(placeholder){ pathInput.placeholder = placeholder; }
      refreshEditSSHKeySelect(initialValue);
      select.addEventListener('change', () => {
        const value = select.value;
        if(value === SSH_KEY_PATH_OPTION){
          pathInput.classList.remove('hidden');
          if(!hidden.value || hidden.value.startsWith(SSH_KEY_REFERENCE_PREFIX)){
            hidden.value = pathInput.value.trim();
          }
          pathInput.focus();
        } else if(value){
          pathInput.classList.add('hidden');
          pathInput.value = '';
          hidden.value = value;
        } else {
          pathInput.classList.add('hidden');
          pathInput.value = '';
          hidden.value = '';
        }
      });
      pathInput.addEventListener('input', () => {
        hidden.value = pathInput.value.trim();
      });
      if(managerBtn){
        managerBtn.addEventListener('click', async (evt) => {
          evt.preventDefault();
          await openEditSSHKeyManager();
        });
      }
    }

    async function openEditSSHKeyManager(){
      if(!editSSHModal){ return; }
      await ensureSSHKeysLoaded(true);
      renderEditSSHKeyList();
      if(editSSHViewer){
        editSSHViewer.classList.add('hidden');
        editSSHViewer.textContent = '';
      }
      editSSHModal.classList.add('active');
    }

    function closeEditSSHKeyManager(){
      if(!editSSHModal){ return; }
      editSSHModal.classList.remove('active');
      if(editSSHViewer){
        editSSHViewer.classList.add('hidden');
        editSSHViewer.textContent = '';
      }
    }

    function renderEditSSHKeyList(){
      if(!editSSHList){ return; }
      if(sshKeysCache.length === 0){
        editSSHList.innerHTML = '<div class="muted">No SSH keys saved yet. Add one below.</div>';
        return;
      }
      editSSHList.innerHTML = sshKeysCache.map(key => {
        const added = formatTimestamp(key.created_at);
        return `
          <div class="key-card">
            <div style="display:flex; flex-direction:column; gap:.2rem;">
              <strong>${escapeHTML(key.name)}</strong>
              <span class="muted" style="font-size:.85rem;">Fingerprint: ${escapeHTML(key.fingerprint)}</span>
              <span class="muted" style="font-size:.85rem;">Added: ${escapeHTML(added)}</span>
            </div>
            <div class="key-card-actions">
              <button type="button" class="btn btn-outline" onclick="viewEditSSHKey(${key.id})">View</button>
              <button type="button" class="btn btn-outline" onclick="selectEditSSHKeyFromManager(${key.id})">Use</button>
              <button type="button" class="btn btn-secondary" onclick="deleteEditSSHKey(${key.id})">Delete</button>
            </div>
          </div>
        `;
      }).join('');
    }

    async function addEditSSHKey(){
      if(!editNewSSHKeyName || !editNewSSHKeyContent){ return; }
      const name = editNewSSHKeyName.value.trim();
      const content = editNewSSHKeyContent.value.trim();
      if(!name || !content){
        showToast({ message: 'Provide both a name and key content.', duration: 4000 });
        return;
      }
      try {
        const response = await json('/api/ssh-keys', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name, private_key: content })
        });
        editNewSSHKeyName.value = '';
        editNewSSHKeyContent.value = '';
        await ensureSSHKeysLoaded(true);
        renderEditSSHKeyList();
        refreshEditSSHKeySelect(response.reference || `${SSH_KEY_REFERENCE_PREFIX}${response.id}`);
        showToast({ message: 'SSH key saved and selected.', duration: 4000 });
        closeEditSSHKeyManager();
      } catch(err){
        showToast({ message: 'Failed to save SSH key: ' + err.message, duration: 6000 });
      }
    }

    async function deleteEditSSHKey(id){
      if(!confirm('Delete this SSH key? This cannot be undone.')){ return; }
      try {
        await json(`/api/ssh-keys/${id}`, { method: 'DELETE' });
        sshKeysCache = sshKeysCache.filter(key => key.id !== id);
        renderEditSSHKeyList();
        refreshEditSSHKeySelect();
        const hidden = document.getElementById('edit-ssh-key-hidden');
        if(hidden && hidden.value === `${SSH_KEY_REFERENCE_PREFIX}${id}`){
          setEditSSHKeySelection('');
        }
        showToast({ message: 'SSH key deleted.', duration: 4000 });
      } catch(err){
        showToast({ message: 'Failed to delete SSH key: ' + err.message, duration: 6000 });
      }
    }

    async function viewEditSSHKey(id){
      if(!editSSHViewer){ return; }
      try {
        const detail = await json(`/api/ssh-keys/${id}`);
        editSSHViewer.textContent = detail.private_key || 'Key unavailable';
        editSSHViewer.classList.remove('hidden');
      } catch(err){
        showToast({ message: 'Failed to load SSH key: ' + err.message, duration: 6000 });
      }
    }

    function selectEditSSHKeyFromManager(id){
      setEditSSHKeySelection(`${SSH_KEY_REFERENCE_PREFIX}${id}`);
      closeEditSSHKeyManager();
    }

    function renderEditValidationResults(result){
      if(!editValidationResults){ return; }
      let html = '';
      const errors = Array.isArray(result.errors) ? result.errors : [];
      const warnings = Array.isArray(result.warnings) ? result.warnings : [];
      const tests = result.tests || {};

      if(errors.length){
        html += '<h4>Errors</h4>';
        errors.forEach(err => { html += `<div class="validation-item validation-error">❌ ${escapeHTML(err)}</div>`; });
      }
      if(warnings.length){
        html += '<h4>Warnings</h4>';
        warnings.forEach(warn => { html += `<div class="validation-item validation-warning">⚠️ ${escapeHTML(warn)}</div>`; });
      }
      if(tests){
        html += '<h4>Connectivity Tests</h4>';
        if(tests.ping){
          const ping = tests.ping;
          const timeDisplay = typeof ping.time_ms === 'number' ? ping.time_ms.toFixed(1) + 'ms' : (ping.time_ms || '--');
          if(ping.success){
            html += `<div class="validation-item validation-success">✅ Ping test passed (${escapeHTML(timeDisplay)})</div>`;
          } else {
            html += `<div class="validation-item validation-error">❌ Ping test failed: ${escapeHTML(ping.error || 'Unknown error')}</div>`;
          }
        }
        if(tests.ssh_port){
          const sshTest = tests.ssh_port;
          if(sshTest.success){
            html += `<div class="validation-item validation-success">✅ SSH port ${(sshTest.port || '22')} accepted</div>`;
          } else {
            html += `<div class="validation-item validation-error">❌ ${escapeHTML(sshTest.error || 'SSH port failed validation')}</div>`;
          }
        }
        if(tests.ports){
          Object.entries(tests.ports).forEach(([port, data]) => {
            if(data.success){
              html += `<div class="validation-item validation-success">✅ ${escapeHTML(port)} is accessible</div>`;
            } else {
              html += `<div class="validation-item validation-warning">⚠️ ${escapeHTML(port)} is not accessible</div>`;
            }
          });
        }
        if(tests.ssh_key){
          html += '<div class="validation-item validation-success">✅ SSH key file exists</div>';
        }
      }
      if(result.valid){
        html += '<div class="validation-item validation-success">✅ Device configuration is valid</div>';
      }
      editValidationResults.innerHTML = html;
      editValidationResults.classList.remove('hidden');
    }

    function renderEditSummary(){
      if(!editSummary || !editState.template){ return; }
      const cfg = editState.deviceConfig;
      const portValue = cfg.meta?.ssh_port || '22';
      const portLabel = portValue === '22' ? '22 (default)' : portValue;
      editSummary.innerHTML = `
        <div>
          <strong>${escapeHTML(cfg.name || '')}</strong>
        </div>
        <div><strong>Host:</strong> ${escapeHTML(cfg.host || '')}</div>
        <div><strong>Type:</strong> ${escapeHTML(editState.template.name)} (${escapeHTML(cfg.platform || '')})</div>
        <div><strong>SSH Port:</strong> ${escapeHTML(portLabel)}</div>
        <div><strong>User:</strong> ${escapeHTML(cfg.user || 'Not specified')}</div>
        ${cfg.ssh_key ? `<div><strong>SSH Key:</strong> ${escapeHTML(cfg.ssh_key)}</div>` : ''}
      `;
      editSummary.classList.remove('hidden');
    }

    async function handleEditValidate(){
      if(editState.isValidating){ return; }
      if(!validateEditForm()){
        setEditStep(EDIT_STEPS.CONFIG);
        return;
      }
      editState.isValidating = true;
      setEditActionState();
      setEditStep(EDIT_STEPS.VALIDATION);
      editValidationLoading?.classList.remove('hidden');
      editValidationResults?.classList.add('hidden');
      editSummary?.classList.add('hidden');
      try {
        const payload = buildEditPayload();
        const result = await json('/api/devices/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        editState.validation = result;
        renderEditValidationResults(result);
        renderEditSummary();
      } catch(err){
        console.error('Validation failed', err);
        showToast({ message: 'Validation failed: ' + err.message, duration: 6000 });
      } finally {
        editState.isValidating = false;
        editValidationLoading?.classList.add('hidden');
        setEditActionState();
      }
    }

    async function handleEditSave(){
      if(editState.isSaving){ return; }
      if(!validateEditForm()){
        setEditStep(EDIT_STEPS.CONFIG);
        return;
      }
      editState.isSaving = true;
      setEditActionState();
      try {
        const payload = buildEditPayload();
        await json(`/api/devices/${editState.deviceId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        showToast({ message: 'Device updated successfully.', duration: 4000 });
        closeEditOverlay();
        await loadDevices();
      } catch(err){
        console.error('Failed to save device', err);
        showToast({ message: 'Failed to save device: ' + err.message, duration: 6000 });
      } finally {
        editState.isSaving = false;
        setEditActionState();
      }
    }

    function closeOpenMenu(){
      if(!openMenuState){ return; }
      const { menu, button } = openMenuState;
      menu.classList.add('hidden');
      button.setAttribute('aria-expanded','false');
      openMenuState = null;
    }

    function toggleDeviceMenu(button, menu){
      if(openMenuState && openMenuState.menu === menu){
        closeOpenMenu();
        return;
      }
      closeOpenMenu();
      menu.classList.remove('hidden');
      button.setAttribute('aria-expanded','true');
      openMenuState = { menu, button };
    }

    async function editDevice(id){
      closeOpenMenu();
      openEditOverlay();
      setEditLoading(true, 'Loading device...');
      try {
        const [templates] = await Promise.all([
          ensureTemplatesLoaded()
        ]);
        const device = await json(`/api/devices/${id}`);
        let metaObj = {};
        if(device.meta){
          if(typeof device.meta === 'string'){
            try { metaObj = device.meta ? JSON.parse(device.meta) : {}; }
            catch { metaObj = {}; }
          } else if(typeof device.meta === 'object' && device.meta !== null){
            metaObj = device.meta;
          }
        }
        const template = templates.find(t => t.kind === device.kind && t.platform === device.platform);
        if(!template){
          showToast({ message: 'No template matches this device type. Unable to edit.', duration: 6000 });
          closeEditOverlay();
          return;
        }
        await ensureSSHKeysLoaded();

        const cfg = { meta: { ...(metaObj || {}) } };
        template.fields.forEach(field => {
          if(Object.prototype.hasOwnProperty.call(device, field.name)){
            cfg[field.name] = device[field.name] ?? '';
          } else if(Object.prototype.hasOwnProperty.call(cfg.meta, field.name)){
            cfg[field.name] = cfg.meta[field.name];
          }
        });
        cfg.name = device.name || cfg.name || '';
        cfg.host = device.host || cfg.host || '';
        cfg.user = device.user || cfg.user || '';
        cfg.ssh_key = device.ssh_key || cfg.ssh_key || '';
        if(cfg.meta?.ssh_port){
          cfg.ssh_port = cfg.meta.ssh_port;
        }
        cfg.kind = template.kind;
        cfg.platform = template.platform;

        editState.deviceId = id;
        editState.template = template;
        editState.deviceConfig = cfg;
        editTitle.textContent = `Edit ${device.name || 'Device'}`;
        editSubtitle.textContent = `${template.name} • ${template.platform}`;
        renderEditForm();
        editValidationResults.innerHTML = '';
        editSummary.innerHTML = '';
        editValidationResults.classList.add('hidden');
        editSummary.classList.add('hidden');
        setEditLoading(false);
        setEditStep(EDIT_STEPS.CONFIG);
      } catch(err){
        console.error('Failed to load device for editing', err);
        showToast({ message: 'Failed to load device: ' + err.message, duration: 6000 });
        closeEditOverlay();
      }
    }

    function drawLineChart(canvas, seriesList){
      const ctx = canvas.getContext('2d');
      const w = canvas.width;
      const h = canvas.height;
      ctx.clearRect(0, 0, w, h);

      const activeSeries = (seriesList || []).filter(s => s && Array.isArray(s.points) && s.points.length > 0);
      if(activeSeries.length === 0){
        ctx.font = '12px sans-serif';
        ctx.fillStyle = '#94a3b8';
        ctx.fillText('no data', 8, h - 8);
        return;
      }

      const xs = activeSeries.flatMap(s => s.points.map(p => p.ts));
      const ys = activeSeries.flatMap(s => s.points.map(p => p.value));

      const minX = Math.min(...xs);
      const maxX = Math.max(...xs);
      let minY = Math.min(...ys);
      let maxY = Math.max(...ys);
      if(!isFinite(minY) || !isFinite(maxY)){
        ctx.font = '12px sans-serif';
        ctx.fillStyle = '#94a3b8';
        ctx.fillText('no data', 8, h - 8);
        return;
      }
      if(minY === maxY){
        const padding = minY === 0 ? 1 : Math.abs(minY) * 0.1;
        minY -= padding;
        maxY += padding;
      }

      const pad = 6;
      const spanX = Math.max(1, maxX - minX);
      const spanY = Math.max(1e-6, maxY - minY);
      function xCoord(t){ return pad + (w - 2 * pad) * (t - minX) / spanX; }
      function yCoord(v){ return h - pad - (h - 2 * pad) * (v - minY) / spanY; }

      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
      ctx.lineWidth = 1.5;

      activeSeries.forEach(series => {
        const points = series.points.slice().sort((a, b) => a.ts - b.ts);
        ctx.beginPath();
        ctx.strokeStyle = series.color || '#2563eb';
        ctx.moveTo(xCoord(points[0].ts), yCoord(points[0].value));
        for(let i = 1; i < points.length; i++){
          ctx.lineTo(xCoord(points[i].ts), yCoord(points[i].value));
        }
        ctx.stroke();
      });

      ctx.font = '12px sans-serif';
      let legendY = 14;
      activeSeries.forEach(series => {
        const lastPoint = series.points[series.points.length - 1];
        const lastValue = lastPoint ? lastPoint.value : null;
        const valueText = lastValue != null && isFinite(lastValue) ? lastValue.toFixed(1) : '--';
        const label = `${series.label || series.key}: ${valueText}${series.unit ? ' ' + series.unit : ''}`;
        ctx.fillStyle = series.color || '#2563eb';
        ctx.fillRect(6, legendY - 7, 8, 8);
        ctx.fillStyle = '#1f2937';
        ctx.fillText(label, 18, legendY);
        legendY += 14;
      });
    }

    function extractMetricValue(entry){
      if(entry == null){ return null; }
      const v = entry.value;
      if(typeof v === 'number'){ return v; }
      if(v && typeof v === 'object'){
        if('Valid' in v && !v.Valid){ return null; }
        if(typeof v.Float64 === 'number'){ return v.Float64; }
        if(typeof v.value === 'number'){ return v.value; }
      }
      if(typeof entry.Value === 'number'){ return entry.Value; }
      return null;
    }

    async function fetchMetricAverage(deviceId, metric, sinceISO){
      const rows = await json(`/api/metrics?device_id=${deviceId}&metric=${metric}&since=${encodeURIComponent(sinceISO)}&limit=1000`);
      const values = (rows || [])
        .map(extractMetricValue)
        .filter(v => typeof v === 'number' && isFinite(v));
      if(values.length === 0){
        return null;
      }
      const sum = values.reduce((acc, value) => acc + value, 0);
      return sum / values.length;
    }

    async function drawDeviceMetrics(device){
      if(device.pending_delete_at){ return; }
      const canvas = document.getElementById(`c-${device.id}`);
      if(!canvas){ return; }
      const since = new Date(Date.now() - 24 * 3600e3).toISOString();

      const series = await Promise.all(METRIC_SERIES.map(async def => {
        try {
          const rows = await json(`/api/metrics?device_id=${device.id}&metric=${def.key}&since=${encodeURIComponent(since)}&limit=1000`);
          const points = (rows || []).map(row => {
            const tsRaw = row.ts || row.TS || row.timestamp;
            const value = extractMetricValue(row);
            const ts = tsRaw ? new Date(tsRaw).getTime() : NaN;
            return { ts, value };
          }).filter(p => isFinite(p.ts) && typeof p.value === 'number' && isFinite(p.value));
          return { ...def, points };
        } catch (err) {
          return { ...def, points: [] };
        }
      }));

      drawLineChart(canvas, series);
    }

    function promptDeleteDevice(id){
      const device = devices.find(dev => dev.id === id);
      const deviceName = device?.name || '';
      const labelName = deviceName ? `"${deviceName}"` : 'this device';
      openConfirmDialog({
        title: 'Delete device',
        message: `Schedule deletion to allow a 20-second undo window. Delete now removes ${labelName} immediately with no undo.`,
        confirmText: 'Schedule deletion',
        variant: 'secondary',
        onConfirm: () => performDeleteDevice(id, deviceName),
        renderExtra: (container) => {
          const expected = deviceName || device?.host || '';
          if(!expected){ return; }
          const block = document.createElement('div');
          block.className = 'confirm-extra-block';
          const label = document.createElement('label');
          label.textContent = `Type ${expected} to delete immediately (no undo).`;
          const input = document.createElement('input');
          input.type = 'text';
          input.placeholder = expected;
          const button = document.createElement('button');
          button.type = 'button';
          setButtonVariant(button, 'danger');
          button.textContent = 'Delete now';
          button.disabled = true;
          input.addEventListener('input', () => {
            button.disabled = input.value.trim() !== expected;
          });
          button.addEventListener('click', () => {
            if(button.disabled){ return; }
            closeConfirmDialog();
            performDeleteDeviceImmediate(id, deviceName);
          });
          block.appendChild(label);
          block.appendChild(input);
          block.appendChild(button);
          container.appendChild(block);
        }
      });
    }

    async function performDeleteDevice(id, name){
      const currentDevice = devices.find(dev => dev.id === id);
      const displayName = name || currentDevice?.name || '';
      try {
        const res = await json(`/api/devices/${id}`, {method:'DELETE'});
        await loadDevices();
        if(res.pending_delete_at){
          const deadline = new Date(res.pending_delete_at).getTime();
          const remaining = deadline - Date.now();
          if(remaining > 0){ setTimeout(loadDevices, remaining + 1500); }
        }
        const baseMessage = displayName ? `Device "${displayName}" scheduled for deletion.` : 'Device scheduled for deletion.';
        const undoSeconds = 20;
        const countdownMessage = (seconds) => {
          const windowText = seconds > 0 ? `Undo within ${seconds}s.` : 'Undo window expired.';
          return `${baseMessage} ${windowText}`;
        };
        showToast({ message: countdownMessage, countdownSeconds: undoSeconds, duration: undoSeconds * 1000, actionText: 'Undo', onAction: async () => {
          const restored = await restoreDevice(id);
          if(restored){
            const successMessage = displayName ? `Deletion cancelled for "${displayName}".` : 'Deletion cancelled.';
            showToast({ message: successMessage, duration: 4000 });
          }
          return restored;
        }});
      } catch(e){
        console.error('Failed to schedule device deletion', e);
        showToast({ message: `Failed to delete device: ${e.message}`, duration: 6000, type: 'error' });
      }
    }

    async function performDeleteDeviceImmediate(id, name){
      const currentDevice = devices.find(dev => dev.id === id);
      const displayName = name || currentDevice?.name || '';
      try {
        await json(`/api/devices/${id}?immediate=true`, {method:'DELETE'});
        await loadDevices();
        showToast({ message: displayName ? `Device "${displayName}" deleted.` : 'Device deleted.', duration: 5000, type: 'success' });
      } catch(e){
        console.error('Failed to delete device immediately', e);
        showToast({ message: `Failed to delete device: ${e.message}`, duration: 6000, type: 'error' });
      }
    }

    async function restoreDevice(id){
      try {
        await json('/api/devices/restore', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({device_id:id})});
        await loadDevices();
        return true;
      } catch(e){
        console.error('Failed to restore device', e);
        showToast({ message: `Failed to restore device: ${e.message}`, duration: 6000, type: 'error' });
        return false;
      }
    }

    const TASK_CONFIRM_COPY = {
      reboot: {
        title: 'Reboot device',
        confirmText: 'Reboot',
        variant: 'danger',
        message: (label) => `Rebooting ${label} will interrupt active sessions and temporarily disconnect it. Continue?`
      },
      refresh_firewall: {
        title: 'Refresh firewall rules',
        confirmText: 'Refresh firewall',
        variant: 'primary',
        message: (label) => `Push the latest firewall configuration to ${label}?`
      },
      refresh_wireless: {
        title: 'Refresh wireless settings',
        confirmText: 'Refresh wireless',
        variant: 'primary',
        message: (label) => `Apply the latest wireless settings to ${label}?`
      }
    };

    function promptTaskConfirmation(device, kind){
      const copy = TASK_CONFIRM_COPY[kind] || {
        title: 'Run task',
        confirmText: 'Run task',
        variant: 'primary',
        message: (label) => `Run this task for ${label}?`
      };
      const labelName = device?.name ? `"${device.name}"` : 'this device';
      openConfirmDialog({
        title: copy.title,
        message: typeof copy.message === 'function' ? copy.message(labelName) : (copy.message || `Run this task for ${labelName}?`),
        confirmText: copy.confirmText,
        variant: copy.variant,
        onConfirm: () => runTask(device.id, kind)
      });
    }

    navTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const viewId = tab.dataset.view;
        setView(viewId || 'overview');
      });
    });

    if(logsForm){
      logsForm.addEventListener('submit', (evt) => {
        evt.preventDefault();
        loadActivityLogs();
      });
    }
    if(logsResetBtn){
      logsResetBtn.addEventListener('click', () => {
        if(logsForm){ logsForm.reset(); }
        loadActivityLogs();
      });
    }

    if(deviceSelectAllBtn){
      deviceSelectAllBtn.addEventListener('click', () => {
        if(!Array.isArray(devices) || devices.length === 0){ return; }
        if(deviceSelection.size === devices.length){
          deviceSelection.clear();
        } else {
          devices.forEach(device => deviceSelection.add(device.id));
        }
        renderDeviceTable();
      });
    }
    if(deviceTableMaster){
      deviceTableMaster.addEventListener('change', () => {
        if(!Array.isArray(devices)){ return; }
        if(deviceTableMaster.checked){
          devices.forEach(device => deviceSelection.add(device.id));
        } else {
          deviceSelection.clear();
        }
        renderDeviceTable();
      });
    }
    if(deviceExportSelectedBtn){
      deviceExportSelectedBtn.addEventListener('click', () => {
        if(!Array.isArray(devices) || devices.length === 0){
          showToast({ message: 'No devices available to export.', duration: 4000, type: 'info' });
          return;
        }
        const selected = devices.filter(device => deviceSelection.has(device.id));
        const stamp = new Date().toISOString().replace(/[:]/g, '-').slice(0,19);
        exportDevicesAsJSON(selected, `pulseops-devices-selected-${stamp}.json`);
      });
    }
    if(deviceExportAllBtn){
      deviceExportAllBtn.addEventListener('click', () => {
        if(!Array.isArray(devices) || devices.length === 0){
          showToast({ message: 'No devices available to export.', duration: 4000, type: 'info' });
          return;
        }
        const stamp = new Date().toISOString().replace(/[:]/g, '-').slice(0,19);
        exportDevicesAsJSON(devices, `pulseops-devices-${stamp}.json`);
      });
    }

    if(settingsEmailEnabled){ settingsEmailEnabled.addEventListener('change', updateEmailFieldState); }
    if(settingsThemeSelect){
      settingsThemeSelect.addEventListener('change', () => {
        if(window.themeManager) {
          window.themeManager.setTheme(settingsThemeSelect.value);
        }
      });
    }
    if(settingsEmailClear){
      settingsEmailClear.addEventListener('change', () => {
        if(settingsEmailClear.checked && settingsEmailPassword){
          settingsEmailPassword.value = '';
        }
      });
    }
    if(settingsEmailPassword){
      settingsEmailPassword.addEventListener('input', () => {
        if(settingsEmailPassword.value && settingsEmailClear){
          settingsEmailClear.checked = false;
        }
      });
    }
    if(settingsForm){
      settingsForm.addEventListener('submit', async (evt) => {
        evt.preventDefault();
        try {
          if(settingsStatus){ settingsStatus.textContent = 'Saving…'; }
          const payload = collectSettingsPayload();
          const response = await json('/api/settings', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });
          applySettingsToForm(response || {});
          if(settingsStatus){
            settingsStatus.textContent = 'Settings saved.';
            setTimeout(() => { if(settingsStatus?.textContent === 'Settings saved.') { settingsStatus.textContent = ''; } }, 4000);
          }
          showToast({ message: 'Settings saved.', duration: 4000, type: 'success' });
        } catch(err){
          console.error('Settings save failed', err);
          if(settingsStatus){ settingsStatus.textContent = ''; }
          showToast({ message: 'Failed to save settings: ' + err.message, duration: 6000, type: 'error' });
        }
      });
    }

    if(insightsSelect){
      insightsSelect.addEventListener('change', () => {
        const value = Number(insightsSelect.value);
        if(Number.isFinite(value) && value > 0){
          insightsState.deviceId = value;
          renderInsightsForDevice(value);
        } else {
          insightsState.deviceId = null;
          if(insightsRefreshBtn){ insightsRefreshBtn.disabled = true; }
          showInsightsEmpty();
        }
      });
    }
    if(insightsRefreshBtn){
      insightsRefreshBtn.addEventListener('click', () => {
        if(insightsState.deviceId){
          renderInsightsForDevice(insightsState.deviceId);
        }
      });
    }

    if(insightsRefreshBtn){ insightsRefreshBtn.disabled = true; }
    if(logsStatus){ logsStatus.textContent = 'Apply filters to explore device and system logs.'; }
    if(settingsForm){ updateEmailFieldState(); }

    if(confirmCancelBtn){ confirmCancelBtn.addEventListener('click', closeConfirmDialog); }
    if(confirmConfirmBtn){
      confirmConfirmBtn.addEventListener('click', async () => {
        const handler = confirmState.onConfirm;
        closeConfirmDialog();
        if(typeof handler === 'function'){
          try {
            await handler();
          } catch(err){
            console.error('Confirmed action failed', err);
            showToast({ message: 'Action failed: ' + err.message, duration: 6000, type: 'error' });
          }
        }
      });
    }
    if(confirmOverlay){ confirmOverlay.addEventListener('click', (evt) => { if(evt.target === confirmOverlay){ closeConfirmDialog(); } }); }
    if(editBackBtn){ editBackBtn.addEventListener('click', () => {
      if(editState.step === EDIT_STEPS.CONFIG){
        closeEditOverlay();
      } else {
        setEditStep(EDIT_STEPS.CONFIG);
      }
    }); }
    if(editValidateBtn){ editValidateBtn.addEventListener('click', () => { handleEditValidate(); }); }
    if(editSaveBtn){ editSaveBtn.addEventListener('click', () => { handleEditSave(); }); }
    if(editCloseBtn){ editCloseBtn.addEventListener('click', () => { closeEditOverlay(); }); }
    if(editOverlay){ editOverlay.addEventListener('click', (evt) => { if(evt.target === editOverlay){ closeEditOverlay(); } }); }
    if(editSSHCloseBtn){ editSSHCloseBtn.addEventListener('click', () => { closeEditSSHKeyManager(); }); }
    if(editSaveSSHKeyBtn){ editSaveSSHKeyBtn.addEventListener('click', () => { addEditSSHKey(); }); }
    if(editSSHModal){ editSSHModal.addEventListener('click', (evt) => { if(evt.target === editSSHModal){ closeEditSSHKeyManager(); } }); }
    document.addEventListener('click', (evt) => {
      if(!openMenuState){ return; }
      const { menu, button } = openMenuState;
      if(menu.contains(evt.target) || button.contains(evt.target)){ return; }
      closeOpenMenu();
    });
    document.addEventListener('keydown', (evt) => {
      if(evt.key !== 'Escape'){ return; }
      if(confirmOverlay && !confirmOverlay.classList.contains('hidden')){
        closeConfirmDialog();
        evt.preventDefault();
        evt.stopPropagation();
        return;
      }
      if(editSSHModal && editSSHModal.classList.contains('active')){
        closeEditSSHKeyManager();
        evt.preventDefault();
        evt.stopPropagation();
        return;
      }
      if(editOverlay && editState.open){
        closeEditOverlay();
        evt.preventDefault();
        evt.stopPropagation();
        return;
      }
      if(openMenuState){
        closeOpenMenu();
        evt.preventDefault();
        evt.stopPropagation();
      }
    });

    async function loadDevices(){
      try {
        const data = await json('/api/devices');
        devices = data;
        renderDevices();
        refreshDeviceFilters();
        renderDeviceTable();
        refreshInsightsSelector();
      } catch(e){
        console.error('Failed to load devices', e);
      }
    }

    function renderDevices(){
      const grid = document.getElementById('devices');
      closeOpenMenu();
      grid.innerHTML = '';
      clearCountdowns();
      const seenIds = new Set();
      const activeDevices = [];
      for(const d of devices){
        if(d.pending_delete_at){
          clearTaskPoller(d.id);
          deviceTasks.delete(d.id);
          expandedTaskPanels.delete(d.id);
          continue;
        }
        activeDevices.push(d);
      }

      if(overviewEmptyState){
        overviewEmptyState.classList.toggle('hidden', activeDevices.length > 0);
      }
      if(activeDevices.length === 0){
        return;
      }

      for(const d of activeDevices){
        seenIds.add(d.id);
        const card = deviceCard(d);
        grid.appendChild(card);
        const toggle = document.getElementById(`logs-toggle-${d.id}`);
        if(toggle){
          toggle.addEventListener('click', () => {
            const current = logExpansionState.get(d.id) || false;
            logExpansionState.set(d.id, !current);
            loadDeviceLogs(d.id);
          });
        }
        if(!d.pending_delete_at){
          refreshLatest(d);
          drawDeviceMetrics(d);
          loadDeviceLogs(d.id);
        }
        loadDeviceTasks(d.id);
      }
      taskRefreshTimers.forEach((timer, id) => {
        if(!seenIds.has(id)){
          clearTimeout(timer);
          taskRefreshTimers.delete(id);
          deviceTasks.delete(id);
        }
      });
      Array.from(expandedTaskPanels).forEach((id) => {
        if(!seenIds.has(id)){
          expandedTaskPanels.delete(id);
        }
      });
    }

    function startRefreshLoop(){
      if(latestInterval){ clearInterval(latestInterval); }
      latestInterval = setInterval(()=>{
        devices.filter(d=>!d.pending_delete_at).forEach(d => {
          refreshLatest(d);
          loadDeviceLogs(d.id);
        });
      }, 10000);
    }

    // Key Management Functions
    let keysData = [];
    const keysState = { loaded: false, isLoading: false };
    const keysLoadingEl = document.getElementById('keys-loading');
    const keysEmptyEl = document.getElementById('keys-empty');
    const keysListEl = document.getElementById('keys-list');
    const keysAddBtn = document.getElementById('keys-add-btn');
    const keysRefreshBtn = document.getElementById('keys-refresh-btn');
    const addKeyModal = document.getElementById('add-key-modal');
    const addKeyForm = document.getElementById('add-key-form');
    const addKeyNameInput = document.getElementById('add-key-name');
    const addKeyContentInput = document.getElementById('add-key-content');
    const addKeyCancelBtn = document.getElementById('add-key-cancel');
    const addKeySaveBtn = document.getElementById('add-key-save');

    async function ensureKeysLoaded(forceReload = false) {
      if (!forceReload && keysState.loaded) return;
      if (keysState.isLoading) return;

      keysState.isLoading = true;
      showKeysLoading();

      try {
        keysData = await json('/api/ssh-keys-usage');
        keysState.loaded = true;
        renderKeysList();
      } catch (err) {
        console.error('Failed to load SSH keys:', err);
        showKeysError('Failed to load SSH keys: ' + err.message);
      } finally {
        keysState.isLoading = false;
      }
    }

    function showKeysLoading() {
      if (keysLoadingEl) keysLoadingEl.classList.remove('hidden');
      if (keysEmptyEl) keysEmptyEl.classList.add('hidden');
      if (keysListEl) keysListEl.classList.add('hidden');
    }

    function showKeysEmpty() {
      if (keysLoadingEl) keysLoadingEl.classList.add('hidden');
      if (keysEmptyEl) keysEmptyEl.classList.remove('hidden');
      if (keysListEl) keysListEl.classList.add('hidden');
    }

    function showKeysError(message) {
      if (keysLoadingEl) {
        keysLoadingEl.textContent = message;
        keysLoadingEl.classList.remove('hidden');
      }
      if (keysEmptyEl) keysEmptyEl.classList.add('hidden');
      if (keysListEl) keysListEl.classList.add('hidden');
    }

    function renderKeysList() {
      if (!keysListEl) return;

      if (!keysData || keysData.length === 0) {
        showKeysEmpty();
        return;
      }

      if (keysLoadingEl) keysLoadingEl.classList.add('hidden');
      if (keysEmptyEl) keysEmptyEl.classList.add('hidden');
      keysListEl.classList.remove('hidden');

      const fragment = document.createDocumentFragment();

      keysData.forEach(key => {
        const keyCard = createKeyCard(key);
        fragment.appendChild(keyCard);
      });

      keysListEl.innerHTML = '';
      keysListEl.appendChild(fragment);
    }

    function createKeyCard(key) {
      const usageText = key.usage_count === 0 ? 'Not used' :
                       key.usage_count === 1 ? '1 device' :
                       `${key.usage_count} devices`;

      const card = el('div', { class: 'key-card' },
        el('div', { class: 'key-card-header' },
          el('div', { class: 'key-card-title' },
            el('h3', { class: 'key-name' }, key.name),
            el('span', { class: 'key-fingerprint' }, key.fingerprint)
          ),
          el('div', { class: 'key-card-actions' },
            el('button', {
              class: 'btn btn-outline btn-sm',
              onclick: () => viewKeyDetails(key.id)
            }, '👁️ View'),
            el('button', {
              class: 'btn danger btn-sm',
              onclick: () => deleteKey(key.id, key.name)
            }, '🗑️ Delete')
          )
        ),
        el('div', { class: 'key-card-meta' },
          el('div', { class: 'key-meta-item' },
            el('span', { class: 'key-meta-label' }, 'Created:'),
            el('span', { class: 'key-meta-value' }, formatTimestamp(key.created_at))
          ),
          el('div', { class: 'key-meta-item' },
            el('span', { class: 'key-meta-label' }, 'Usage:'),
            el('span', {
              class: `key-meta-value ${key.usage_count > 0 ? 'key-used' : 'key-unused'}`
            }, usageText)
          )
        )
      );

      if (key.usage_count > 0) {
        const usageList = el('div', { class: 'key-usage-list' },
          el('h4', { class: 'key-usage-title' }, 'Used by:'),
          ...key.used_by.map(device =>
            el('div', { class: 'key-usage-device' },
              el('span', { class: 'device-name' }, device.device_name),
              el('span', { class: 'device-host muted' }, device.device_host),
              el('span', { class: 'device-kind muted' }, device.device_kind)
            )
          )
        );
        card.appendChild(usageList);
      }

      return card;
    }

    async function viewKeyDetails(keyId) {
      try {
        const keyDetail = await json(`/api/ssh-keys/${keyId}`);
        showConfirm(
          'SSH Key Details',
          `Key: ${keyDetail.name}\nFingerprint: ${keyDetail.fingerprint}\nCreated: ${formatTimestamp(keyDetail.created_at)}`,
          'Close',
          () => {},
          [
            {
              text: 'Copy Private Key',
              action: () => {
                navigator.clipboard.writeText(keyDetail.private_key).then(() => {
                  showToast({ message: 'Private key copied to clipboard', duration: 3000, type: 'success' });
                }).catch(() => {
                  showToast({ message: 'Failed to copy to clipboard', duration: 3000, type: 'error' });
                });
              }
            }
          ]
        );
      } catch (err) {
        showToast({ message: 'Failed to load key details: ' + err.message, duration: 5000, type: 'error' });
      }
    }

    async function deleteKey(keyId, keyName) {
      const key = keysData.find(k => k.id === keyId);
      if (key && key.usage_count > 0) {
        showToast({
          message: `Cannot delete key "${keyName}" - it is used by ${key.usage_count} device(s)`,
          duration: 5000,
          type: 'error'
        });
        return;
      }

      showConfirm(
        'Delete SSH Key',
        `Are you sure you want to delete the SSH key "${keyName}"? This action cannot be undone.`,
        'Delete',
        async () => {
          try {
            await json(`/api/ssh-keys/${keyId}`, { method: 'DELETE' });
            showToast({ message: 'SSH key deleted successfully', duration: 4000, type: 'success' });
            await ensureKeysLoaded(true);
          } catch (err) {
            showToast({ message: 'Failed to delete SSH key: ' + err.message, duration: 5000, type: 'error' });
          }
        }
      );
    }

    function openAddKeyModal() {
      if (addKeyModal) {
        addKeyNameInput.value = '';
        addKeyContentInput.value = '';
        addKeyModal.classList.remove('hidden');
        addKeyNameInput.focus();
      }
    }

    function closeAddKeyModal() {
      if (addKeyModal) {
        addKeyModal.classList.add('hidden');
      }
    }

    async function saveNewKey() {
      const name = addKeyNameInput.value.trim();
      const content = addKeyContentInput.value.trim();

      if (!name) {
        showToast({ message: 'Key name is required', duration: 3000, type: 'error' });
        addKeyNameInput.focus();
        return;
      }

      if (!content) {
        showToast({ message: 'Private key content is required', duration: 3000, type: 'error' });
        addKeyContentInput.focus();
        return;
      }

      try {
        await json('/api/ssh-keys', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name, private_key: content })
        });

        showToast({ message: 'SSH key saved successfully', duration: 4000, type: 'success' });
        closeAddKeyModal();
        await ensureKeysLoaded(true);
      } catch (err) {
        showToast({ message: 'Failed to save SSH key: ' + err.message, duration: 5000, type: 'error' });
      }
    }

    // Event listeners for key management
    if (keysAddBtn) keysAddBtn.addEventListener('click', openAddKeyModal);
    if (keysRefreshBtn) keysRefreshBtn.addEventListener('click', () => ensureKeysLoaded(true));
    if (addKeyCancelBtn) addKeyCancelBtn.addEventListener('click', closeAddKeyModal);
    if (addKeySaveBtn) addKeySaveBtn.addEventListener('click', saveNewKey);
    if (addKeyForm) {
      addKeyForm.addEventListener('submit', (e) => {
        e.preventDefault();
        saveNewKey();
      });
    }

    // Close modal when clicking outside
    if (addKeyModal) {
      addKeyModal.addEventListener('click', (e) => {
        if (e.target === addKeyModal) {
          closeAddKeyModal();
        }
      });
    }

    async function init(){
      // Initialize theme from stored preference
      if(window.themeManager) {
        const storedTheme = window.themeManager.getStoredTheme();
        if(storedTheme) {
          window.themeManager.setTheme(storedTheme);
        }
      }

      await checkAuthStatus();
      await loadDevices();
      startRefreshLoop();
      setInterval(loadDevices, 30000);
    }
    init();
  </script>
</body>
</html>
